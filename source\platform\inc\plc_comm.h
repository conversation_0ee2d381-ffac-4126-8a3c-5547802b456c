#ifndef _PLC_COMM_H_
#define _PLC_COMM_H_

#include "plc_reftable.h"
#include "common.h"
#include "plc_lib.h"

// PLC初始化配置
typedef struct {
    uint32_t appSize;               // PLC应用程序大小
    uint8_t appMd5[16];             // PLC应用程序MD5校验
    unsigned char autoStart;        // 自动启动标志
    unsigned char enableTick;       // 使能心跳标志
} s_plcInitConfig;

// 外部变量声明
extern s_plcRefTab g_BIplcRefTab[];
extern s_plcRefTab g_ANAplcRefTab[];

// 函数声明

/**
 * @brief 初始化PLC通信模块 - 参照103协议的初始化流程
 * @return 成功返回SUCC，失败返回FAIL
 */
int initPlcComm(void);

/**
 * @brief PLC通信主任务 - 参照103协议的主循环处理
 * @note 在主循环中周期性调用，建议10ms周期
 */
void plcCommTask(void);

void PLC_test_task(void);

/**
 * @brief 检查PLC数据变化 - 统一调用数字量和模拟量变化检测
 * @return 无返回值
 */
void checkPlcDataChange(void);

/**
 * @brief 同步所有变化的数据到PLC - 参照103协议的批量同步机制
 * @return 同步的数据项数量
 */
int syncAllChangedDataToPlc(void);

/**
 * @brief 从PLC同步所有输出数据 - 参照103协议的批量同步机制
 * @return 同步的数据项数量
 */
int syncAllOutputDataFromPlc(void);

#endif // _PLC_COMM_H_
