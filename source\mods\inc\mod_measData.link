void linkModMeasData(s_modMeasData * dp, char ia, char ib, char ic, char i0, char ua, char ub, char uc, char u0, char ux)
{
	dp->chan[0] = ia;
	dp->chan[1] = ib;
	dp->chan[2] = ic;
	dp->chan[3] = i0;
	dp->chan[4] = ua;
	dp->chan[5] = ub;
	dp->chan[6] = uc;
	dp->chan[7] = u0;
	dp->chan[8] = ux;
	
	dp->F1 = &modGeneral.F1_f;
	dp->F2 = &modGeneral.F2_f;	
	
	dp->fourier_val[0] = &modCur.value[0];
	dp->fourier_val[1] = &modCur.value[1];
	dp->fourier_val[2] = &modCur.value[2];
	dp->fourier_val[3] = &modCur.value[3];
	dp->fourier_val[4] = &modVol.value[0];
	dp->fourier_val[5] = &modVol.value[1];
	dp->fourier_val[6] = &modVol.value[2];
	dp->fourier_val[7] = &modVol.value[3];
	dp->fourier_val[8] = &modVol.value[4];
	
	return;
}
