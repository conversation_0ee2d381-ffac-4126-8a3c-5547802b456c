#ifndef __TCP_PLC_SERVER_H__
#define __TCP_PLC_SERVER_H__


#ifdef __cplusplus
extern "C"{
#endif


#include "main.h"
#include "lwip.h"
#include "tcp.h"

#define TCP_PLC_REC_BUF_LEN  1024    // PLC TCP服务器接收缓冲区长度
    
typedef struct
{
    uint32_t rev_len;
    uint8_t  rev_buf[ TCP_PLC_REC_BUF_LEN ];
} TCP_PLC_RECBUF_ST, *P_TCP_PLC_RECBUF_ST;
    
static err_t tcp_plc_server_recv(void *arg, struct tcp_pcb *tpcb,
                                struct pbuf *p, err_t err);
static err_t tcp_plc_server_accept(void *arg, struct tcp_pcb *newpcb, err_t err);

// PLC TCP服务器接口声明
void tcp_plc_server_init(void);
int get_tcp_plc_server_data(char *buf, int len);
int send_tcp_plc_server_data(char *buff_tx, int buff_tx_len);
void send_tcp_plc_server_data_plc(uint8_t *buff_tx, uint16_t buff_tx_len);
int get_tcp_plc_server_status(void);

#ifdef __cplusplus
}
#endif

#endif
