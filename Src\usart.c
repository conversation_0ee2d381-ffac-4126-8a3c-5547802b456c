/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    usart.c
  * @brief   This file provides code for the configuration
  *          of the USART instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "usart.h"

/* USER CODE BEGIN 0 */
#include "uart.h" //NOTEL USART
/* USER CODE END 0 */

UART_HandleTypeDef huart7;
UART_HandleTypeDef huart2;
UART_HandleTypeDef huart3;
UART_HandleTypeDef huart6;
DMA_HandleTypeDef hdma_uart7_rx;
DMA_HandleTypeDef hdma_usart2_rx;
DMA_HandleTypeDef hdma_usart3_rx;
DMA_HandleTypeDef hdma_usart6_rx;

/* UART7 init function */
void MX_UART7_Init(void)
{

  /* USER CODE BEGIN UART7_Init 0 */

  /* USER CODE END UART7_Init 0 */

  /* USER CODE BEGIN UART7_Init 1 */

  /* USER CODE END UART7_Init 1 */
  huart7.Instance = UART7;
  huart7.Init.BaudRate = 115200;
  huart7.Init.WordLength = UART_WORDLENGTH_8B;
  huart7.Init.StopBits = UART_STOPBITS_1;
  huart7.Init.Parity = UART_PARITY_NONE;
  huart7.Init.Mode = UART_MODE_TX_RX;
  huart7.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart7.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart7) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN UART7_Init 2 */

  /* USER CODE END UART7_Init 2 */

}
/* USART2 init function */

void MX_USART2_UART_Init(void)
{

  /* USER CODE BEGIN USART2_Init 0 */

  /* USER CODE END USART2_Init 0 */

  /* USER CODE BEGIN USART2_Init 1 */

  /* USER CODE END USART2_Init 1 */
  huart2.Instance = USART2;
  huart2.Init.BaudRate = 115200;
  huart2.Init.WordLength = UART_WORDLENGTH_8B;
  huart2.Init.StopBits = UART_STOPBITS_1;
  huart2.Init.Parity = UART_PARITY_NONE;
  huart2.Init.Mode = UART_MODE_TX_RX;
  huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart2.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart2) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART2_Init 2 */

  /* USER CODE END USART2_Init 2 */

}
/* USART3 init function */

void MX_USART3_UART_Init(void)
{

  /* USER CODE BEGIN USART3_Init 0 */

  /* USER CODE END USART3_Init 0 */

  /* USER CODE BEGIN USART3_Init 1 */

  /* USER CODE END USART3_Init 1 */
  huart3.Instance = USART3;
  huart3.Init.BaudRate = 115200;
  huart3.Init.WordLength = UART_WORDLENGTH_8B;
  huart3.Init.StopBits = UART_STOPBITS_1;
  huart3.Init.Parity = UART_PARITY_NONE;
  huart3.Init.Mode = UART_MODE_TX_RX;
  huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart3.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart3) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART3_Init 2 */

  /* USER CODE END USART3_Init 2 */

}
/* USART6 init function */

void MX_USART6_UART_Init(void)
{

  /* USER CODE BEGIN USART6_Init 0 */

  /* USER CODE END USART6_Init 0 */

  /* USER CODE BEGIN USART6_Init 1 */

  /* USER CODE END USART6_Init 1 */
  huart6.Instance = USART6;
  huart6.Init.BaudRate = 115200;
  huart6.Init.WordLength = UART_WORDLENGTH_8B;
  huart6.Init.StopBits = UART_STOPBITS_1;
  huart6.Init.Parity = UART_PARITY_NONE;
  huart6.Init.Mode = UART_MODE_TX_RX;
  huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart6.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart6) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART6_Init 2 */

  /* USER CODE END USART6_Init 2 */

}

void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(uartHandle->Instance==UART7)
  {
  /* USER CODE BEGIN UART7_MspInit 0 */

  /* USER CODE END UART7_MspInit 0 */
    /* UART7 clock enable */
    __HAL_RCC_UART7_CLK_ENABLE();

    __HAL_RCC_GPIOE_CLK_ENABLE();
    /**UART7 GPIO Configuration
    PE7     ------> UART7_RX
    PE8     ------> UART7_TX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_7|GPIO_PIN_8;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART7;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

    /* UART7 DMA Init */
    /* UART7_RX Init */
    hdma_uart7_rx.Instance = DMA1_Stream3;
    hdma_uart7_rx.Init.Channel = DMA_CHANNEL_5;
    hdma_uart7_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_uart7_rx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_uart7_rx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_uart7_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_uart7_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_uart7_rx.Init.Mode = DMA_NORMAL;
    hdma_uart7_rx.Init.Priority = DMA_PRIORITY_LOW;
    hdma_uart7_rx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_uart7_rx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmarx,hdma_uart7_rx);

    /* UART7 interrupt Init */
    HAL_NVIC_SetPriority(UART7_IRQn, 6, 0);
    HAL_NVIC_EnableIRQ(UART7_IRQn);
  /* USER CODE BEGIN UART7_MspInit 1 */

  /* USER CODE END UART7_MspInit 1 */
  }
  else if(uartHandle->Instance==USART2)
  {
  /* USER CODE BEGIN USART2_MspInit 0 */

  /* USER CODE END USART2_MspInit 0 */
    /* USART2 clock enable */
    __HAL_RCC_USART2_CLK_ENABLE();

    __HAL_RCC_GPIOD_CLK_ENABLE();
    /**USART2 GPIO Configuration
    PD5     ------> USART2_TX
    PD6     ------> USART2_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_5|GPIO_PIN_6;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART2;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

    /* USART2 DMA Init */
    /* USART2_RX Init */
    hdma_usart2_rx.Instance = DMA1_Stream5;
    hdma_usart2_rx.Init.Channel = DMA_CHANNEL_4;
    hdma_usart2_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_usart2_rx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart2_rx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart2_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart2_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart2_rx.Init.Mode = DMA_NORMAL;
    hdma_usart2_rx.Init.Priority = DMA_PRIORITY_LOW;
    hdma_usart2_rx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart2_rx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmarx,hdma_usart2_rx);

    /* USART2 interrupt Init */
    HAL_NVIC_SetPriority(USART2_IRQn, 6, 0);
    HAL_NVIC_EnableIRQ(USART2_IRQn);
  /* USER CODE BEGIN USART2_MspInit 1 */

  /* USER CODE END USART2_MspInit 1 */
  }
  else if(uartHandle->Instance==USART3)
  {
  /* USER CODE BEGIN USART3_MspInit 0 */

  /* USER CODE END USART3_MspInit 0 */
    /* USART3 clock enable */
    __HAL_RCC_USART3_CLK_ENABLE();

    __HAL_RCC_GPIOD_CLK_ENABLE();
    /**USART3 GPIO Configuration
    PD8     ------> USART3_TX
    PD9     ------> USART3_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_8|GPIO_PIN_9;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART3;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

    /* USART3 DMA Init */
    /* USART3_RX Init */
    hdma_usart3_rx.Instance = DMA1_Stream1;
    hdma_usart3_rx.Init.Channel = DMA_CHANNEL_4;
    hdma_usart3_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_usart3_rx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart3_rx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart3_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart3_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart3_rx.Init.Mode = DMA_NORMAL;
    hdma_usart3_rx.Init.Priority = DMA_PRIORITY_LOW;
    hdma_usart3_rx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart3_rx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmarx,hdma_usart3_rx);

    /* USART3 interrupt Init */
    HAL_NVIC_SetPriority(USART3_IRQn, 6, 0);
    HAL_NVIC_EnableIRQ(USART3_IRQn);
  /* USER CODE BEGIN USART3_MspInit 1 */

  /* USER CODE END USART3_MspInit 1 */
  }
  else if(uartHandle->Instance==USART6)
  {
  /* USER CODE BEGIN USART6_MspInit 0 */

  /* USER CODE END USART6_MspInit 0 */
    /* USART6 clock enable */
    __HAL_RCC_USART6_CLK_ENABLE();

    __HAL_RCC_GPIOC_CLK_ENABLE();
    /**USART6 GPIO Configuration
    PC6     ------> USART6_TX
    PC7     ------> USART6_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_USART6;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    /* USART6 DMA Init */
    /* USART6_RX Init */
    hdma_usart6_rx.Instance = DMA2_Stream1;
    hdma_usart6_rx.Init.Channel = DMA_CHANNEL_5;
    hdma_usart6_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_usart6_rx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart6_rx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart6_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart6_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart6_rx.Init.Mode = DMA_NORMAL;
    hdma_usart6_rx.Init.Priority = DMA_PRIORITY_LOW;
    hdma_usart6_rx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart6_rx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmarx,hdma_usart6_rx);

    /* USART6 interrupt Init */
    HAL_NVIC_SetPriority(USART6_IRQn, 6, 0);
    HAL_NVIC_EnableIRQ(USART6_IRQn);
  /* USER CODE BEGIN USART6_MspInit 1 */

  /* USER CODE END USART6_MspInit 1 */
  }
}

void HAL_UART_MspDeInit(UART_HandleTypeDef* uartHandle)
{

  if(uartHandle->Instance==UART7)
  {
  /* USER CODE BEGIN UART7_MspDeInit 0 */

  /* USER CODE END UART7_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_UART7_CLK_DISABLE();

    /**UART7 GPIO Configuration
    PE7     ------> UART7_RX
    PE8     ------> UART7_TX
    */
    HAL_GPIO_DeInit(GPIOE, GPIO_PIN_7|GPIO_PIN_8);

    /* UART7 DMA DeInit */
    HAL_DMA_DeInit(uartHandle->hdmarx);

    /* UART7 interrupt Deinit */
    HAL_NVIC_DisableIRQ(UART7_IRQn);
  /* USER CODE BEGIN UART7_MspDeInit 1 */

  /* USER CODE END UART7_MspDeInit 1 */
  }
  else if(uartHandle->Instance==USART2)
  {
  /* USER CODE BEGIN USART2_MspDeInit 0 */

  /* USER CODE END USART2_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART2_CLK_DISABLE();

    /**USART2 GPIO Configuration
    PD5     ------> USART2_TX
    PD6     ------> USART2_RX
    */
    HAL_GPIO_DeInit(GPIOD, GPIO_PIN_5|GPIO_PIN_6);

    /* USART2 DMA DeInit */
    HAL_DMA_DeInit(uartHandle->hdmarx);

    /* USART2 interrupt Deinit */
    HAL_NVIC_DisableIRQ(USART2_IRQn);
  /* USER CODE BEGIN USART2_MspDeInit 1 */

  /* USER CODE END USART2_MspDeInit 1 */
  }
  else if(uartHandle->Instance==USART3)
  {
  /* USER CODE BEGIN USART3_MspDeInit 0 */

  /* USER CODE END USART3_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART3_CLK_DISABLE();

    /**USART3 GPIO Configuration
    PD8     ------> USART3_TX
    PD9     ------> USART3_RX
    */
    HAL_GPIO_DeInit(GPIOD, GPIO_PIN_8|GPIO_PIN_9);

    /* USART3 DMA DeInit */
    HAL_DMA_DeInit(uartHandle->hdmarx);

    /* USART3 interrupt Deinit */
    HAL_NVIC_DisableIRQ(USART3_IRQn);
  /* USER CODE BEGIN USART3_MspDeInit 1 */

  /* USER CODE END USART3_MspDeInit 1 */
  }
  else if(uartHandle->Instance==USART6)
  {
  /* USER CODE BEGIN USART6_MspDeInit 0 */

  /* USER CODE END USART6_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART6_CLK_DISABLE();

    /**USART6 GPIO Configuration
    PC6     ------> USART6_TX
    PC7     ------> USART6_RX
    */
    HAL_GPIO_DeInit(GPIOC, GPIO_PIN_6|GPIO_PIN_7);

    /* USART6 DMA DeInit */
    HAL_DMA_DeInit(uartHandle->hdmarx);

    /* USART6 interrupt Deinit */
    HAL_NVIC_DisableIRQ(USART6_IRQn);
  /* USER CODE BEGIN USART6_MspDeInit 1 */

  /* USER CODE END USART6_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */

//NOTEL 设置通信参数
void  USART3_deInit(void)
{
    __HAL_UART_DISABLE_IT(&huart3, UART_IT_IDLE);
    HAL_UART_DMAStop(&huart3);
    HAL_UART_DeInit(&huart3);
}

void USART2_UART_Init_BTL(uint32_t BaudRate, uint32_t  WordLength, uint32_t StopBits, uint32_t Parity )
{
    huart2.Instance = USART2;
    huart2.Init.BaudRate = BaudRate;
    if(WordLength == 8)
      huart2.Init.WordLength = UART_WORDLENGTH_8B;
  else
      huart2.Init.WordLength = UART_WORDLENGTH_9B;

  if(StopBits == 1)
      huart2.Init.StopBits = UART_STOPBITS_1;
  else
      huart2.Init.StopBits = UART_STOPBITS_2;

  if(Parity == 0)
      huart2.Init.Parity = UART_PARITY_NONE;
  else
  if(Parity == 1)
     huart2.Init.Parity = UART_PARITY_ODD;  //奇
  else
     huart2.Init.Parity = UART_PARITY_EVEN; //偶

    huart2.Init.Mode = UART_MODE_TX_RX;
    huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart2.Init.OverSampling = UART_OVERSAMPLING_16;
    if (HAL_UART_Init(&huart2) != HAL_OK)
    {
      Error_Handler();
    }

}
void USART7_UART_Init_BTL(uint32_t BaudRate, uint32_t  WordLength, uint32_t StopBits, uint32_t Parity )
{
    huart7.Instance = UART7;
    huart7.Init.BaudRate = BaudRate;
    if(WordLength == 8)
      huart7.Init.WordLength = UART_WORDLENGTH_8B;
  else
      huart7.Init.WordLength = UART_WORDLENGTH_9B;

  if(StopBits == 1)
      huart7.Init.StopBits = UART_STOPBITS_1;
  else
      huart7.Init.StopBits = UART_STOPBITS_2;

  if(Parity == 0)
      huart7.Init.Parity = UART_PARITY_NONE;
  else
  if(Parity == 1)
     huart7.Init.Parity = UART_PARITY_ODD;  //奇
  else
     huart7.Init.Parity = UART_PARITY_EVEN; //偶

    huart7.Init.Mode = UART_MODE_TX_RX;
    huart7.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart7.Init.OverSampling = UART_OVERSAMPLING_16;
    if (HAL_UART_Init(&huart7) != HAL_OK)
    {
      Error_Handler();
    }

}
void USART3_UART_Init_BTL(uint32_t BaudRate, uint32_t  WordLength, uint32_t StopBits, uint32_t Parity )
{
  huart3.Instance = USART3;
  huart3.Init.BaudRate = BaudRate;
  if(WordLength == 8)
      huart3.Init.WordLength = UART_WORDLENGTH_8B;
  else
      huart3.Init.WordLength = UART_WORDLENGTH_9B;

  if(StopBits == 1)
      huart3.Init.StopBits = UART_STOPBITS_1;
  else
      huart3.Init.StopBits = UART_STOPBITS_2;

  if(Parity == 0)
      huart3.Init.Parity = UART_PARITY_NONE;
  else
  if(Parity == 1)
     huart3.Init.Parity = UART_PARITY_ODD;  //奇
  else
     huart3.Init.Parity = UART_PARITY_EVEN; //偶

  huart3.Init.Mode = UART_MODE_TX_RX;
  huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart3.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart3) != HAL_OK)
  {
    Error_Handler();
  }

}
void USART6_UART_Init_BTL(uint32_t BaudRate, uint32_t  WordLength, uint32_t StopBits, uint32_t Parity )
{
  huart6.Instance = USART6;
  huart6.Init.BaudRate = BaudRate;
  if(WordLength == 8)
      huart6.Init.WordLength = UART_WORDLENGTH_8B;
  else
      huart6.Init.WordLength = UART_WORDLENGTH_9B;

  if(StopBits == 1)
      huart6.Init.StopBits = UART_STOPBITS_1;
  else
      huart6.Init.StopBits = UART_STOPBITS_2;

  if(Parity == 0)
      huart6.Init.Parity = UART_PARITY_NONE;
  else
  if(Parity == 1)
     huart6.Init.Parity = UART_PARITY_ODD;  //奇
  else
     huart6.Init.Parity = UART_PARITY_EVEN; //偶

  huart6.Init.Mode = UART_MODE_TX_RX;
  huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart6.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart6) != HAL_OK)
  {
    Error_Handler();
  }

}

//设置通信参数
//BaudRate: 115200 / 57600/38400/19200/14400/9600/4800/2400/1200
//Parity: 0/1/2   :无校验 /奇校验 /偶校验
//WordLength: 8 /9
//StopBits: 1/2
void USART2_UART_SET_BTL(uint32_t BaudRate, uint32_t Parity, uint32_t  WordLength, uint32_t StopBits )
{
    if((BaudRate != 115200) &&  (BaudRate != 57600) &&  (BaudRate != 38400) && (BaudRate != 19200) && (BaudRate != 14400) &&
        (BaudRate != 9600) && (BaudRate != 4800)  && (BaudRate != 2400)  && (BaudRate != 1200) )
        return;

    if((Parity != 0) && (Parity != 1) && (Parity != 2))
        return;

    if((WordLength != 8) &&  (WordLength != 9))
        return;

    if((StopBits != 1) &&  (StopBits != 2))
        return;

    __HAL_UART_DISABLE_IT(&huart2, UART_IT_IDLE);
    HAL_UART_DMAStop(&huart2);
    HAL_UART_DeInit(&huart2);

    USART2_UART_Init_BTL(BaudRate, WordLength, StopBits, Parity);
    __HAL_UART_ENABLE_IT(&huart2, UART_IT_IDLE);                    //使能串口空闲中断
    HAL_UART_Receive_DMA(&huart2, g_rx0_buffer, COM_RX_BUFFER_SIZE);//启动串口DMA接收

}
void USART7_UART_SET_BTL(uint32_t BaudRate, uint32_t Parity, uint32_t  WordLength, uint32_t StopBits )
{
    if((BaudRate != 115200) &&  (BaudRate != 57600) &&  (BaudRate != 38400) && (BaudRate != 19200) && (BaudRate != 14400) &&
        (BaudRate != 9600) && (BaudRate != 4800)  && (BaudRate != 2400)  && (BaudRate != 1200) )
        return;

    if((Parity != 0) && (Parity != 1) && (Parity != 2))
        return;

    if((WordLength != 8) &&  (WordLength != 9))
        return;

    if((StopBits != 1) &&  (StopBits != 2))
        return;

    __HAL_UART_DISABLE_IT(&huart7, UART_IT_IDLE);
    HAL_UART_DMAStop(&huart7);
    HAL_UART_DeInit(&huart7);

    USART7_UART_Init_BTL(BaudRate, WordLength, StopBits, Parity);
    __HAL_UART_ENABLE_IT(&huart7, UART_IT_IDLE);                    //使能串口空闲中断
    HAL_UART_Receive_DMA(&huart7, g_rx1_buffer, COM_RX_BUFFER_SIZE);//启动串口DMA接收

}
void USART3_UART_SET_BTL(uint32_t BaudRate, uint32_t Parity, uint32_t  WordLength, uint32_t StopBits )
{
     if((BaudRate != 115200) &&  (BaudRate != 57600) &&  (BaudRate != 38400) && (BaudRate != 19200) && (BaudRate != 14400) &&
        (BaudRate != 9600) && (BaudRate != 4800)  && (BaudRate != 2400)  && (BaudRate != 1200) )
        return;

     if((Parity != 0) && (Parity != 1) && (Parity != 2))
        return;

     if((WordLength != 8) &&  (WordLength != 9))
        return;

     if((StopBits != 1) &&  (StopBits != 2))
        return;

    __HAL_UART_DISABLE_IT(&huart3, UART_IT_IDLE);
    HAL_UART_DMAStop(&huart3);
    HAL_UART_DeInit(&huart3);

    USART3_UART_Init_BTL(BaudRate, WordLength, StopBits, Parity);
    __HAL_UART_ENABLE_IT(&huart3, UART_IT_IDLE);                    //使能串口空闲中断
    HAL_UART_Receive_DMA(&huart3, g_rx2_buffer, COM_RX_BUFFER_SIZE);//启动串口DMA接收

}
void USART6_UART_SET_BTL(uint32_t BaudRate, uint32_t Parity, uint32_t  WordLength, uint32_t StopBits )
{
     if((BaudRate != 115200) &&  (BaudRate != 57600) &&  (BaudRate != 38400) && (BaudRate != 19200) && (BaudRate != 14400) &&
        (BaudRate != 9600) && (BaudRate != 4800)  && (BaudRate != 2400)  && (BaudRate != 1200) )
        return;

     if((Parity != 0) && (Parity != 1) && (Parity != 2))
        return;

     if((WordLength != 8) &&  (WordLength != 9))
        return;

     if((StopBits != 1) &&  (StopBits != 2))
        return;

    __HAL_UART_DISABLE_IT(&huart6, UART_IT_IDLE);
    HAL_UART_DMAStop(&huart6);
    HAL_UART_DeInit(&huart6);

    USART6_UART_Init_BTL(BaudRate, WordLength, StopBits, Parity);
    __HAL_UART_ENABLE_IT(&huart6, UART_IT_IDLE);                    //使能串口空闲中断
    HAL_UART_Receive_DMA(&huart6, g_rx3_buffer, COM_RX_BUFFER_SIZE);//启动串口DMA接收

}
//NOTE

/* USER CODE END 1 */
