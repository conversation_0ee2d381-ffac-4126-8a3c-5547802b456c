#include "mod_overVol.h"
/*过压保护*/
void runModOverVol(s_modOverVol* dp)
{
	unsigned char flg_con,flg_qd,flg_op_ov;
	unsigned char flg_op_ov_alarm;

	//启动
	flg_con = dp->setSwit_ov; //投退条件
	//最大相间电压大于0.95倍过电压保护定值
	overRelay(&(dp->flg_ov_qd_val),dp->u_max,dp->set_qd_u,&(dp->timebuf_qd),0.95,1);
	if(dp->flg_ov_qd_val && flg_con)
	{
		//dp->qd_ov = 1;
		flg_qd = 0x1;
	}
	else 
	{
		//dp->qd_ov = 0;
		flg_qd = 0;
	}
	//展宽
	dp->qd_ov = extRelay( flg_qd, &(dp->timebuf_zk),500);
	overRelays(&(dp->flg_ov_op_val),dp->uab,dp->set_u_ov,dp->set_qd_u,dp->timebuf_op,1,3);
	flg_op_ov = (threeToOne(dp->flg_ov_op_val)) & dp->qd_ov;
	//动作
	dp->op_ov = delayRelay(flg_op_ov,&(dp->timebuf_dly),dp->set_t_ov);

  	//告警
    overRelays(&(dp->flg_ov_op_val_alarm),dp->uab,dp->set_u_ov_alarm,dp->set_qd_u_alarm,dp->timebuf_op_alarm,1,3);
	flg_op_ov_alarm = (threeToOne(dp->flg_ov_op_val_alarm)) & dp->setSwit_ov_alarm;
	dp->op_ov_alarm = fDelayReturnRelay(flg_op_ov_alarm,&(dp->timebuf_dly_alarm),dp->set_t_ov_alarm,500);

	return;
	
}

int initModOverVol(s_modOverVol* dp, char* dpName)
{
	dp->dpName = dpName;
	//注册开关量
	regBI(dpName,"op_ov",0,&dp->op_ov);//动作信号
    regBI(dpName, "op_ov_alarm", 0, &dp->op_ov_alarm);
	//regBI(dpName,"ovbi_ov",0,&dp->ovbi_ov);
	//注册模拟量
	
	//注册启动元件
	regQdElement(&dp->qd_ov);

    //注册告警信息
	regChkElement(&dp->op_ov_alarm);

	//注册动作元件
	regTripElement(&dp->op_ov,&dp->trip_matrix);
	
	//加入队列
	addTaskLevel2(runModOverVol, dp);
	
	
}
int initModParmOverVol(s_modOverVol* dp)
{
	//获取定值
	//dp->vbi_ov = getSetByDesc("vbi_ov");	//软压板
	//dp->ovbi_ov = dp->vbi_ov;
	dp->setSwit_ov = getSetByDesc("setSwit_ov");	//控制字
	dp->setSwit_ov_alarm = getSetByDesc("setSwit_ov_alarm");	//控制字
	dp->set_u_ov = getSetByDesc("set_u_ov");		//过压保护定值
	dp->set_u_ov_alarm = getSetByDesc("set_u_ov_alarm");		//过压保护定值
	dp->set_qd_u = dp->set_u_ov*95/100;	 //0.95是启动门槛
	dp->set_qd_u_alarm = dp->set_u_ov_alarm*95/100;	 //0.95是启动门槛
	dp->set_t_ov = getSetByDesc("set_t_ov");	//过压时间定值
	dp->set_t_ov_alarm = getSetByDesc("set_t_ov_alarm");	//过压时间定值

	dp->trip_matrix = getSetByDesc("trip_matrix_ov");
	return SUCC;
	
}

