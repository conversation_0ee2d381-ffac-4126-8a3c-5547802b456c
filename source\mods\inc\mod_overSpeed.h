#ifndef _MOD_OVERSPEED_H_
#define _MOD_OVERSPEED_H_
#include "task_queue.h"
#include "fun.h"

typedef struct
{
	char* dpName;
	
	//输入信号
	float* fm;
	
	//输出信号
	unsigned char qd_overspeed_qd;
	unsigned char qd_overspeed;
	unsigned char op_overspeed;
	unsigned char op_overspeed_alarm;
	
	//输出模拟量
	
	//定值
	//unsigned char vbi_overspeed;		//软压板
	unsigned char setSwit_overspeed; //控制字
	unsigned char setSwit_overspeed_alarm; //控制字
	int set_i_overspeed;	//过负荷定值
	unsigned int set_t_overspeed;  //过负荷时间
	int set_i_overspeed_alarm;	//过负荷告警定值
	unsigned int set_t_overspeed_alarm;  //过负荷告警时间
	int trip_matrix;	//
	
	//内部用
	int timebuf_qd;
	int timebuf_qd2;
	int timebuf_op;
	int timebuf_alarm;
	unsigned char flg_qd;
	unsigned char flg_alarm;
	
}s_modOverSpeed;

int initModOverSpeed(s_modOverSpeed* dp, char* dpName);
int initModParmOverSpeed(s_modOverSpeed* dp);

#endif