#include "mod_CTFail.h"
/*Ct 断线*/

void runModCTFail(s_modCTFail* dp)
{
	unsigned char flg_con;

	if (*dp->flg_qd == 0)
	{
		flg_con = (3 * (*dp->i2)) > ((*dp->in) * 6 / 100 + (*dp->i_max) * 25 / 100);
		dp->ct_dx = delayRelay(flg_con & dp->setSwit_ct_dx, &dp->timebuf_ct, 10000);
	}	

	return;
}
int initModCTFail(s_modCTFail* dp,char* dpName)
{
	dp->dpName = dpName;
	//注册开关量
	regBI(dpName,"ct_dx", 0, &dp->ct_dx);
	//注册模拟量
	
	//注册告警信息
	regChkElement(&dp->ct_dx);
	
	//加入队列
	addTaskLevel2(runModCTFail,dp);
	return SUCC;
}

int initModParmCTFail(s_modCTFail* dp)
{
	dp->setSwit_ct_dx = getSetByDesc("setSwit_ct_dx");
	
	return SUCC;
}