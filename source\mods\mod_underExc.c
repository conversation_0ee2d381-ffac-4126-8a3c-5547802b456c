#include "mod_underExc.h"
/*失磁保护*/

void runModUnderExc(s_modUnderExc* dp)
{
	unsigned char flg_con, flg_tmp;
	int ue;
	ue = *(dp->fm) * DEFAULT_DIV_VAL;  // Assigning the value of dp->fm to fm

	//投退条件
	flg_con = dp->vbi_exc & dp->setSwit_exc;
	
	flg_tmp = flg_con & (*dp->flg_abc);
	//小于励磁低电压定值	
	dp->qd_exc = ((*(dp->fm)*DEFAULT_DIV_VAL)<dp->set_f_exc_qd) & flg_tmp;
	//动作
	underRelay(&(dp->flg_exc_op_val), &ue, dp->set_f_exc, &(dp->timebuf_for_op), 0.95, 3);
	dp->op_exc = fDelayReturnRelay(dp->flg_exc_op_val & dp->qd_exc & dp->flg_blk,&(dp->timebuf_op),dp->set_t_exc,200);
	
	return;
}

int initModUnderExc(s_modUnderExc* dp, char* dpName)
{
	dp->dpName = dpName;
	
	//注册开关量
	regBI(dpName, "op_exc", 0, &dp->op_exc);	//动作信号
	regBI(dpName, "ovbi_exc", 0, &dp->ovbi_exc);	
	//注册模拟量
	
	//注册启动元件
	regQdElement(&dp->qd_exc);
	
	//注册动作元件
	regTripElement(&dp->op_exc, &dp->trip_matrix);
	
	//加入队列
	addTaskLevel2(runModUnderExc, dp);
	
	return SUCC;
	
}
int initModParmUnderExc(s_modUnderExc* dp)
{
	//获取定值
	
	dp->vbi_exc = getSetByDesc("vbi_exc");	//软压板
	dp->ovbi_exc = dp->vbi_exc;
	dp->setSwit_exc = getSetByDesc("setSwit_exc");	//控制字
	dp->set_f_exc = getSetByDesc( "set_f_exc");	//励磁低电压定值
	dp->set_f_exc_qd = dp->set_f_exc+500;	//+0.05是门槛
	dp->set_t_exc = getSetByDesc( "set_t_exc"); //失磁时间
	dp->trip_matrix = getSetByDesc("trip_matrix_exc");
	return SUCC;
	
}