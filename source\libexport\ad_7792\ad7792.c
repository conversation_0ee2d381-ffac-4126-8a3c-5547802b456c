/**
 * @file ad7792.c
 * <AUTHOR> pinghui
 * @brief ad7792驱动程序
 * @version 0.1
 * @date 2025-06-18
 * 
 * @copyright Copyright (c) 2025
 * 
 */

#include "ad7792.h"
#include <stdio.h>
#include "setting.h"

// 24位ADC转换系数
// double Code_B = -319.750608;   
// double Code_K = 0.000038128525;

// 16位ADC转换系数
double Code_K = 0.00582018;  // 新斜率
double Code_B = 2.15;        // 新截距

// 变量定义
uint8 DC_Workstep0 = 0;
uint8 Init_Flag = 0; // 初始化标志位
int32 sampleData;
int16 sampleData1;
uint8 ADspi_sendbuf[8];
uint8 ADspi_recievebuf[8];
uint8 DC_Channel0 = TEMP_0_STARTCHANNEL;

AVE_DC  DCchannel[AD7792_CHANNEL_NUM];

void ADDelay(uint32 ADD_Time)
{
	while (ADD_Time) {
        ADD_Time--;
    }
}

/**
 * @brief AD7792初始化函数
 * 
 */
void AD7792_Init(void)
{
    uint8_t verifyStatus = 1;
    uint8_t expectedData[3] = {0};

    // AD7792复位
    ADspi_sendbuf[0] = 0xFF;
    ADspi_sendbuf[1] = 0xFF;
    ADspi_sendbuf[2] = 0xFF;
    ADspi_sendbuf[3] = 0xFF;

    HAL_SPI_Transmit(&hspi3, ADspi_sendbuf, 4, HAL_MAX_DELAY);
    ADDelay(720000); // 等待复位完成

    // 写I/O配置寄存器-设置1mA激励电流
    ADspi_sendbuf[0] = 0x28;
    ADspi_sendbuf[1] = 0x03;

    HAL_SPI_Transmit(&hspi3, ADspi_sendbuf, 2, HAL_MAX_DELAY);
    ADDelay(60000); // 等待配置完成

    #ifdef debug_AD7792
    // 读取I/O配置寄存器以验证设置
    expectedData[0] = 0x03; // 预期的1mA激励电流设置
    if (ReadAndVerifyRegister(AD7792_REG_IO, expectedData, 1) == 0) {
        printf("AD7792 I/O configuration verification failed!\n");
        verifyStatus = 0;
    }
    #endif

    // 写AD7792模式寄存器-设置ADC模式为单次模式
    ADspi_sendbuf[0] = 0x08;
    ADspi_sendbuf[1] = 0x20;
    ADspi_sendbuf[2] = 0x0a;

    HAL_SPI_Transmit(&hspi3, ADspi_sendbuf, 3, HAL_MAX_DELAY);
    ADDelay(60000); // 等待模式设置完成

    #ifdef debug_AD7792
    // 读取模式寄存器以验证设置
    expectedData[0] = 0x20;
    expectedData[1] = 0x0a; // 预期的单次模式和64K基准电压设置
    if (ReadAndVerifyRegister(AD7792_REG_MODE, expectedData, 2) == 0) {
        printf("AD7792 mode configuration verification failed!\n");
        verifyStatus = 0;
    }
    #endif

    // 写AD7792配置寄存器
    ADspi_sendbuf[0] = 0x10;
    ADspi_sendbuf[1] = 0x13;
    ADspi_sendbuf[2] = 0x01;

    HAL_SPI_Transmit(&hspi3, ADspi_sendbuf, 3, HAL_MAX_DELAY);
    ADDelay(60000); // 等待双极性模式设置完成

    #ifdef debug_AD7792
    // 读取配置寄存器以验证设置
    expectedData[0] = 0x13;
    expectedData[1] = 0x01;
    if (ReadAndVerifyRegister(AD7792_REG_CONFIG, expectedData, 2) == 0) {
        printf("AD7792 configuration verification failed!\n");
        verifyStatus = 0;
    }
    #endif

    // 初始化温度切换通道
    GPIO_MUX_Init();

    if (VerifyAD7792Communication() == 0) {
        printf("AD7792 communication failed!\n");
    }

    Init_Flag = 1; // 设置初始化标志位

}

void GPIO_MUX_Init(void)
{
    // 使能温度切换通道时钟
    __HAL_RCC_GPIOD_CLK_ENABLE();
    __HAL_RCC_GPIOG_CLK_ENABLE();

    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 配置温度切换通道引脚
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;

    GPIO_InitStruct.Pin = M_WDMUX1_GPIO_PIN | M_WDMUX2_GPIO_PIN;
    HAL_GPIO_Init(M_WDMUX1_GPIO_PORT, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = M_WDMUX3_GPIO_PIN | M_WDMUX4_GPIO_PIN | M_WDMUX5_GPIO_PIN | M_WDMUX6_GPIO_PIN;
    HAL_GPIO_Init(M_WDMUX3_GPIO_PORT, &GPIO_InitStruct);

    // 初始化所有通道为低电平
    M_WDMUX1_SET;
    M_WDMUX2_SET;
    M_WDMUX3_SET;
    M_WDMUX4_SET;
    M_WDMUX5_SET;
    M_WDMUX6_SET;

    // 设置初始通道为温度通道1
    // AD7792_Switch(TEMP_0_STARTCHANNEL);
}

uint8_t VerifyAD7792Communication(void)
{
    uint8_t id_value;
    uint8_t tx_buff[2] = {0};
    uint8_t rx_buff[2] = {0};
    HAL_StatusTypeDef status;

    // 片选拉低（如果使用软件片选）
    // HAL_GPIO_WritePin(AD7792_CS_PORT, AD7792_CS_PIN, GPIO_PIN_RESET);

    // 发送读取ID命令(地址0x20 + 读位0x40 = 0x60)
    tx_buff[0] = ((AD7792_REG_ID<<3) & 0x3F) | 0x40; // RS2, RS1, RS0 + 读标志位
    // status = HAL_SPI_TransmitReceive(&hspi3, tx_buff, rx_buff, 1, HAL_MAX_DELAY);
    HAL_SPI_Transmit(&hspi3, tx_buff, 1, HAL_MAX_DELAY);
    // 接收ID值
    status = HAL_SPI_Receive(&hspi3, rx_buff, 1, HAL_MAX_DELAY);
    if (status != HAL_OK) {
        return 0; // 通信失败
    }

    // 片选拉高（如果使用软件片选）
    // HAL_GPIO_WritePin(AD7792_CS_PORT, AD7792_CS_PIN, GPIO_PIN_SET);

    // 检查ID值是否正确
    if (rx_buff[0] == 0x4A) { // AD7792的ID
        return 1; // 通信成功
    } else {
        return 0; // 通信失败
    }
}

uint8_t ReadAndVerifyRegister(uint8_t regAddr, uint8_t* expectedData, uint8_t bytesNum)
{
    uint8_t readData[3] = {0};
    uint8_t sendData[4] = {0};
    uint8_t i;

    // 构造读命令
    sendData[0] = ((regAddr<<3) & 0x3F) | 0x40; // RS2, RS1, RS0 + 读标志位

    // 发送读命令
    HAL_SPI_Transmit(&hspi3, sendData, 1, HAL_MAX_DELAY);

    // 接收数据
    HAL_SPI_Receive(&hspi3, readData, bytesNum, HAL_MAX_DELAY);

    // 检查读取的数据是否与预期数据匹配
    for (i = 0; i < bytesNum; i++) {
        if (readData[i] != expectedData[i]) {
            return 0; // 数据不匹配，返回失败
        }
    }
    return 1; // 数据匹配，返回成功
}

uint8_t VerifyAD7792RegisiterRW(uint8_t regAddr, uint8_t* writeData, uint8_t bytesNum)
{
    uint8_t readData[3] = {0};
    uint8_t i;

    // 构造写命令：寄存器地址(RS2, RS1, RS0) + 写数据标志位(0)
    ADspi_sendbuf[0] = (regAddr & 0x3F); // RS2, RS1, RS0

    // 复制写数据到发送缓冲区
    for (i = 0; i < bytesNum; i++) {
        ADspi_sendbuf[i + 1] = writeData[i];
    }

    // 发送写命令和数据
    HAL_SPI_Transmit(&hspi3, ADspi_sendbuf, bytesNum + 1, HAL_MAX_DELAY);
    ADDelay(60000); // 等待写操作完成

    // 构造读命令：寄存器地址(RS2, RS1, RS0) + 读数据标志位(1)
    ADspi_sendbuf[0] = (regAddr & 0x3F) | 0x40; // RS2, RS1, RS0 + 读标志位

    // 发送读命令
    HAL_SPI_Transmit(&hspi3, ADspi_sendbuf, 1, HAL_MAX_DELAY);

    // 接收数据
    HAL_SPI_Receive(&hspi3, readData, bytesNum, HAL_MAX_DELAY);

    // 检查读取的数据是否与写入的数据匹配
    for (i = 0; i < bytesNum; i++) {
        if (readData[i] != writeData[i]) {
            return 0; // 数据不匹配，返回失败
        }
    }
    return 1; // 数据匹配，返回成功
}


void AD7792_Switch(uint8 channel)
{
    switch(channel) {
        case TEMP_0_STARTCHANNEL: {         // 切换到温度通道2
            M_WDMUX1_CLR;
            M_WDMUX2_SET;
            M_WDMUX3_CLR;
            M_WDMUX4_CLR;
            M_WDMUX5_CLR;
            M_WDMUX6_CLR;
            break;
        }
        case TEMP_0_STARTCHANNEL + 1: { // 切换到温度通道3
            M_WDMUX1_SET;
            M_WDMUX2_CLR;
            M_WDMUX3_CLR;
            M_WDMUX4_CLR;
            M_WDMUX5_CLR;
            M_WDMUX6_CLR;
            break;
        }
        case TEMP_0_STARTCHANNEL + 2: { // 切换到温度通道4
            M_WDMUX1_CLR;
            M_WDMUX2_CLR;
            M_WDMUX3_SET;
            M_WDMUX4_SET;
            M_WDMUX5_CLR;
            M_WDMUX6_CLR;
            break;
        }
        case TEMP_0_STARTCHANNEL + 3: { // 切换到温度通道5
            M_WDMUX1_CLR;
            M_WDMUX2_CLR;
            M_WDMUX3_CLR;
            M_WDMUX4_SET;
            M_WDMUX5_CLR;
            M_WDMUX6_CLR;
            break;
        }
        case TEMP_0_STARTCHANNEL + 4: { // 切换到温度通道6
            M_WDMUX1_CLR;
            M_WDMUX2_CLR;
            M_WDMUX3_SET;
            M_WDMUX4_CLR;
            M_WDMUX5_CLR;
            M_WDMUX6_CLR;
            break;
        }
        case TEMP_0_STARTCHANNEL + 5: { // 切换到温度通道7
            M_WDMUX1_CLR;
            M_WDMUX2_CLR;
            M_WDMUX3_CLR;
            M_WDMUX4_CLR;
            M_WDMUX5_SET;
            M_WDMUX6_SET;
            break;
        }
        case TEMP_0_STARTCHANNEL + 6: { // 切换到温度通道8
            M_WDMUX1_CLR;
            M_WDMUX2_CLR;
            M_WDMUX3_CLR;
            M_WDMUX4_CLR;
            M_WDMUX5_CLR;
            M_WDMUX6_SET;
            break;
        }
        case TEMP_0_STARTCHANNEL + 7: { // 切换到温度通道1
            M_WDMUX1_SET;
            M_WDMUX2_SET;
            M_WDMUX3_CLR;
            M_WDMUX4_CLR;
            M_WDMUX5_CLR;
            M_WDMUX6_CLR;
            break;
        }
        default: {
            break;
        }
    }
}


/****************************************************************************
 * 函数名称：void readAD7792Data(void)
 * 功能描述：读取AD7792数据
 * 输入参数：无
 * 输出参数：无
 * 返 回 值：无
 * 修改历史：
 * 修改人：
 * 修改日期：
 * 修改内容：
 ****************************************************************************/
void readAD7792Data(void)
{
    if (Init_Flag == 0) {
        return;
    }
    if (DC_Workstep0 == 0) {
        ADspi_sendbuf[0] = 0x58;
        HAL_SPI_Transmit(&hspi3, ADspi_sendbuf, 1, HAL_MAX_DELAY);
        DC_Workstep0++;
    }
    else if (DC_Workstep0 == 1) {
        HAL_SPI_Receive(&hspi3, ADspi_recievebuf, 2, HAL_MAX_DELAY);
        sampleData1 = ADspi_recievebuf[0];
        sampleData = sampleData1 << 8;
        sampleData += (ADspi_recievebuf[1] & 0xFF);

        DCchannel[DC_Channel0].DCvalue = sampleData;
        DCchannel[DC_Channel0].LastPos++;
        if (DCchannel[DC_Channel0].LastPos >= AveNum) {
            CalculateAve(&DCchannel[DC_Channel0]); // 计算直流平均值
            DCchannel[DC_Channel0].LastPos = 0;
        }

        DCchannel[DC_Channel0].DCvalueBuf[DCchannel[DC_Channel0].LastPos] = DCchannel[DC_Channel0].DCvalue;
        if (DC_Channel0 == 0 || DC_Channel0 == 1) {
            Cal_ThermistorTemp(DC_Channel0); // 计算热电阻温度
        }
        AD7792_Switch(DC_Channel0); // 切换到下一个通道
        DC_Channel0++;

        if (DC_Channel0 > TEMP_0_ENDCHANNEL) {
            DC_Channel0 = TEMP_0_STARTCHANNEL; // 重置通道
        }

        ADspi_sendbuf[0] = 0x08; // 发送下一个转换命令
        ADspi_sendbuf[1] = 0x20; // 设置为单次转换模式
        ADspi_sendbuf[2] = 0x0a; // 设置基准电压

        HAL_SPI_Transmit(&hspi3, ADspi_sendbuf, 3, HAL_MAX_DELAY);
        DC_Workstep0++;
    }
    else {
        DC_Workstep0++;
        if (DC_Workstep0 > 11) {
            DC_Workstep0 = 0;
        }
    }
}

/****************************************************************************
 * 函数名称：void CalculateAve(AVE_DC *Temp)
 * 功能描述：计算直流平均值
 * 输入参数：Temp-指向AVE_DC结构体的指针
 * 输出参数：无
 * 返 回 值：无
 * 修改历史：
 * 修改人：
 * 修改日期：
 * 修改内容：
 ****************************************************************************/
void CalculateAve(AVE_DC *Temp)
{
    uint16 i;
    int32 sum = 0;
    int32 SampleBuffer[AveNum] = {0};

    for(i = 0; i < AveNum; i++) {
		SampleBuffer[i] = Temp->DCvalueBuf[i];
	}

    BubbleSort(AveNum, SampleBuffer);

    for(i = 3; i < AveNum - 3; i++) { // 排除最大和最小值
        sum += SampleBuffer[i];
    }

    Temp->AVEdata = sum / (AveNum - 6); // 计算平均值
}

/****************************************************************************
* 函数名称：void BubbleSort(uint32 length, int32 *array)
* 功能描述：冒泡排序
* 输入参数：length-数组长度，array-待排序数组
* 输出参数：无
* 返 回 值：无
* 修改历史：
* 修改人：
* 修改日期：
* 修改内容：
****************************************************************************/
void BubbleSort(uint32 length, int32 *array)
{
    int32 temp = 0;
    uint32 i = 0, j = 0;

    if (length == 0 || array == NULL) {
        return;
    }

    for (i = 0; i < length - 1; i++) {
        for (j = 0; j < length - i - 1; j++) {
            if (array[j] > array[j + 1]) {
                temp = array[j];
                array[j] = array[j + 1];
                array[j + 1] = temp;
            }
        }
    }
}

/****************************************************************************
* 函数名称: void Cal_ThermistorTemp(void)
* 功能描述: 热电阻温度计算
* 输入参数: 无
* 输出参数: 无
* 返 回 值: 无
* 修改历史:
* 修改人:
* 修改日期:
* 修改内容:
****************************************************************************/
void Cal_ThermistorTemp(uint8 DC_Channel)
{
    uint16 CTTP_Type;          // 热电阻类型：0-不使用，1-PT100，2-Cu50，3-Cu53
    uint32 CTTP_RAdr;          // AD采样值地址
    int32 CTTP_VAL1;           // 当前AD采样值
    int32 CTTP_Temp;           // 分度表转换后的温度值
    uint16 CTTP_channalN;      // AD采样通道号
    uint8_t Crt_Com = 0;            //校准命令
    float resist;              // 电阻值
    float result;
    float temp_value;
    char temp_type_name[20]; // 热电阻类型名称
    float temperatureK[8][2];
    char desc[20];

    // 获取AD采样通道号
    CTTP_channalN = DC_Channel;
    // sprintf(temp_type_name, "temp%d_type", CTTP_channalN - TEMP_0_STARTCHANNEL + 1);
    
    // 获取热电阻类型 TODO: 从参数中获取
    // CTTP_Type = getSetByDesc(temp_type_name);
    CTTP_Type = 1;
    
    // 获取AD采样值
    CTTP_VAL1 = (int32)DCchannel[CTTP_channalN].AVEdata;

    sprintf(desc, "temperatureK_%02d_%02d", CTTP_channalN + 1, 0);
    temperatureK[CTTP_channalN][0] = getSetByDesc(desc) / DEFAULT_DIV_VAL;
    sprintf(desc, "temperatureK_%02d_%02d", CTTP_channalN + 1, 1);
    temperatureK[CTTP_channalN][1] = getSetByDesc(desc) / DEFAULT_DIV_VAL;

    // 通过AD值计算阻值
    resist = (temperatureK[CTTP_channalN][0] * Code_K * CTTP_VAL1 + Code_B) * temperatureK[CTTP_channalN][1];

    if(Crt_Com)
    {
        // 执行校准命令
        Temp_Calibrate(CTTP_channalN, Crt_Com);
    }
    
    // 根据当前通道设置的热电阻类型参数设置
    switch(CTTP_Type)
    {
        case 1: // PT100
            CTTP_Temp = Pt100GetTemp(resist);
            if (CTTP_Temp == 99999) {
                temp_value = 999.99; // 异常值处理
            }
            else {
                temp_value = (float)CTTP_Temp / 100.0; // 转换为实际温度值
            }
            break;
            
        case 2: // Cu50
            CTTP_Temp = calcTempCu50(resist);
            if (CTTP_Temp == 99999) {
                temp_value = 999.99;
            }
            else {
                temp_value = (float)CTTP_Temp / 100.0;
            }
            break;
            
        case 3: // Cu53
            CTTP_Temp = calcTempCu53(resist);
            if (CTTP_Temp == 99999) {
                temp_value = 999.99;
            }
            else {
                temp_value = (float)CTTP_Temp / 100.0;
            }
            break;
            
        default: // 不使用该通道
            temp_value = 999.99; // 设置为异常值
            resist = 0;
            break;
    }

    // 设置温度值到AVE_DC结构体
    DCchannel[CTTP_channalN].TempResist = resist; // 保存电阻值
    DCchannel[CTTP_channalN].TempValue = (int32)(CTTP_Temp);
    // return temp_value; // 返回计算后的温度值
    
}

/****************************************************************************
 * 函数名称: int32 Temp_Calibrate(uint8 DC_Channel, uint8 calibType)
 * 功能描述: 温度校准 - 通过调整K1和K2系数来实现
 * 输入参数: DC_Channel - AD采样通道号
 *           calibType - 校准类型：0-零点校准(100Ω对应0℃)，1-满度校准(150Ω对应130.2℃)
 * 输出参数: 无
 * 返 回 值: 0-校准成功，<0-校准失败
 * 修改历史:
 * 修改人:
 * 修改日期:
 * 修改内容:
 ****************************************************************************/
int32 Temp_Calibrate(uint8 DC_Channel, uint8 calibType)
{
    // 校准参数
    static float zeroResistValues[AD7792_CHANNEL_NUM] = {0.0};  // 各通道零点AD值
    static float zeroResistValue = 100.0;                  // 零点电阻值(100Ω)
    static float zeroTempValue = 0.0;                      // 零点温度值(0℃)
    
    static float fullResistValues[AD7792_CHANNEL_NUM] = {0.0};  // 各通道满度AD值
    static float fullResistValue = 150.0;                  // 满度电阻值(150Ω)
    static float fullTempValue = 130.2;                    // 满度温度值(130.2℃)

    static uint8_t channelCalibFlags[AD7792_CHANNEL_NUM] = {0}; // 记录每个通道的校准状态
    uint8_t channelIndex;

    int32_t adValue;
    uint16_t sensorType;
    float measuredResist;
    float result = 0.0;

    // 校验通道号是否在有效范围内
    if (DC_Channel < TEMP_0_STARTCHANNEL || DC_Channel > TEMP_0_ENDCHANNEL) {
        return -1; // 返回错误
    }

    // 计算通道索引
    channelIndex = DC_Channel - TEMP_0_STARTCHANNEL;

    // 获取当前热电阻类型，目前假设为PT100，1-PT100, 2-Cu50, 3-Cu53
    sensorType = 1; // TODO: 从参数中获取实际热电阻类型

    // 获取当前通道的电阻值
    measuredResist = DCchannel[DC_Channel].TempResist; // 获取电阻值

    // 根据校准类型执行不同的操作
    if (calibType == 0) { // 零点校准
        zeroResistValues[channelIndex] = (float)measuredResist;
        channelCalibFlags[channelIndex] |= 0x01;
        // 检查是否已经进行过零点校准
        // 如果零点和满度都已校准，则计算新的K1和K2
        if ((channelCalibFlags[channelIndex] & 0x03) == 0x03) {
            // 计算校准系数K1和K2
            calculateK1K2(channelIndex, sensorType, 
                         zeroResistValues, zeroResistValue,
                         fullResistValues, fullResistValue);
        }
        return 0; // 零点校准成功
    } 
    else if (calibType == 1) { // 满度校准
        fullResistValues[channelIndex] = (float)measuredResist;
        channelCalibFlags[channelIndex] |= 0x02;
        // 如果零点和满度都已校准，则计算新的K1和K2
        if ((channelCalibFlags[channelIndex] & 0x03) == 0x03) {
            // 计算校准系数K1和K2
            calculateK1K2(channelIndex, sensorType, 
                         zeroResistValues, zeroResistValue,
                         fullResistValues, fullResistValue);
        }
        return 0; // 满度校准成功
    } 
    else {
        return -2; // 无效的校准类型
    }
}

/****************************************************************************
 * 函数名称: void calculateK1K2(uint8_t channelIndex, uint8_t sensorType, float *measuredR1, float actualR1, float *measuredR2, float actualR2)
 * 功能描述: 计算K1和K2系数
 * 输入参数: channelIndex - 通道索引
 *           sensorType - 传感器类型
 *          measuredR1 - 测量的零点电阻值数组
 *          actualR1 - 实际零点电阻值
 *          measuredR2 - 测量的满度电阻值数组
 *         actualR2 - 实际满度电阻值
 * 输出参数: 无
 * 返 回 值: 无
 * 修改历史:
 * 修改人:
 * 修改日期:
 * 修改内容:
 * ****************************************************************************/
void calculateK1K2(uint8_t channelIndex, uint8_t sensorType, float *measuredR1, float actualR1,float *measuredR2, float actualR2)
{
    float R1 = measuredR1[channelIndex]; // 测量的零点电阻值
    float R2 = measuredR2[channelIndex]; // 测量的满度电阻值
    float R1n = actualR1; // 零点电阻值
    float R2n = actualR2; // 满度电阻值
    float numerator, denominator;
    float K1_coef, K2_coef;

    // K1_coef
    numerator = (R2n - R1n) * Code_B;
    denominator = R1n * R2 - R2n * R1 + numerator;

    if (fabs(denominator) < 1e-6) {
        // 防止除以零
        //temperatureK[channelIndex][0] = 1.0; // 设置为默认值
        //temperatureK[channelIndex][1] = 1.0; // 设置为默认值

        char desc[64];
        sprintf(desc, "temperatureK_%02d_%02d", channelIndex + 1, 0);
        writeSetToBuf(temperatureK_setting, 1, getSetIdByDesc(desc), (int) 1);

        sprintf(desc, "temperatureK_%02d_%02d", channelIndex + 1, 1);
        writeSetToBuf(temperatureK_setting, 1, getSetIdByDesc(desc), (int) 1);

        writeSetGroup(temperatureK_setting, 1);
        assignSetDataSheet(temperatureK_setting, 1);

        return;
    }

    K1_coef = numerator / denominator;

    // K2_coef
    denominator = (R2 - Code_B) * K1_coef + Code_B;

    if (fabs(denominator) < 1e-6) {
        // 防止除以零
        //temperatureK[channelIndex][0] = 1.0; // 设置为默认值
        //temperatureK[channelIndex][1] = 1.0; // 设置为默认值

        char desc[64];
        sprintf(desc, "temperatureK_%02d_%02d", channelIndex + 1, 0);
        writeSetToBuf(temperatureK_setting, 1, getSetIdByDesc(desc), (int) 1);

        sprintf(desc, "temperatureK_%02d_%02d", channelIndex + 1, 1);
        writeSetToBuf(temperatureK_setting, 1, getSetIdByDesc(desc), (int) 1);

        writeSetGroup(temperatureK_setting, 1);
        assignSetDataSheet(temperatureK_setting, 1);

        return;
    }

    K2_coef = R2n / denominator;

    // 保存系数到全局数组
    //temperatureK[channelIndex][0] = K1_coef; // 保存K1系数
    //temperatureK[channelIndex][1] = K2_coef; // 保存K2系数

    K1_coef = K1_coef * DEFAULT_DIV_VAL;
    K2_coef = K2_coef * DEFAULT_DIV_VAL;

    char desc[64];
    sprintf(desc, "temperatureK_%02d_%02d", channelIndex + 1, 0);
    writeSetToBuf(temperatureK_setting, 1, getSetIdByDesc(desc), (int) K1_coef);

    sprintf(desc, "temperatureK_%02d_%02d", channelIndex + 1, 1);
    writeSetToBuf(temperatureK_setting, 1, getSetIdByDesc(desc), (int) K2_coef);

    writeSetGroup(temperatureK_setting, 1);
    assignSetDataSheet(temperatureK_setting, 1);


}

