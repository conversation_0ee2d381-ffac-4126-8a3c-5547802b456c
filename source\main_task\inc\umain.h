#ifndef _MAIN_H_
#define _MAIN_H_

#ifndef WIN32
#include "stm32f4xx_hal.h"
#include "xsj_lib.h"
#include "udp_server.h"
#include "udp_client.h"
#include "tcp_server.h"
#include "tcp_plc_server.h"
#include "tcp_client.h"
#endif

#include "common.h"
#include "setting.h"
#include "eventRefTable.h"
#include "bi.h"
#include "ana.h"
#include "sample_task.h"
#include "soe_task.h"
#include "reftable103.h"
#include "plc_reftable.h"
#include "plc_comm.h"
#include "lb_task.h"
#include "N103Slave.h"
#include "S103Slave.h"
#include "Modbus.h"
#include "bcode_task.h"
#include "keepStatus.h"
#include "bsp_ad7616.h"
#include "ad7792.h"

void ReadLEDState(void);
void key_task(void);
void ScrProt(void);
void judeg_or_not(void);

#endif
