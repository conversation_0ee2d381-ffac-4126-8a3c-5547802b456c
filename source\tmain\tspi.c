
#include "xsj_lib.h"
#include "stm32f4xx_hal.h"

//spi2: fram, fm25v02
void fram_test(void)
{
	uint32_t x=0;
	uint32_t a = 10000;
	uint8_t d_read[4],d_send[3]={0};
    
	uint32_t data = 0x12345678;
    
	d_send[0]=(data>>16)&0xff;
	d_send[1]=(data>>8)&0xff;
	d_send[2]=(data)&0xff;
    
//	if(HAL_SPI_Transmit(&hspi1,d_send,3,0xFFF)!=HAL_OK)
//  if(fram_send(d_send,3) != HAL_OK)
    if( fram_send(d_send, 3) != HAL_OK)
	    x = 0xffffffff;
	
	while(a--);
    
	
//	if(HAL_SPI_Receive(&hspi1,d_read,3,0xFFF)!=HAL_OK)
    if(fram_recv(d_read, 3) != HAL_OK)    
	    x = 0xffffffff;
    
	printf(" the test results of fm25v02 : %x\r\n", x);
	
    while(1);
}


