/**
 * @file Temp_Referable.c
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @brief PT100、Cu50、Cu53热电阻温度参考表及计算函数
 * @version 0.1
 * @date 2025-07-23
 * 
 * @copyright Copyright (c) 2025
 * 
 */

#include "Temp_ReferTable.h"

//the resist value is multiplied by 100 so that the value could transmit integer.
static const int temp_table_Pt100[203] = {
    7991,	8031,	8070,	8110,	8150,	8189,	8229,	8269,	8308,	8348,   //-51~-42
	8387,	8427,	8467,	8506,	8546,	8585,	8625,	8664,	8704,	8743,   //-41~-32
	8783,	8822,	8862,	8901,	8940,	8980,	9019,	9059,	9098,	9137,   //-31~-22
	9177,	9216,	9255,	9295,	9334,	9373,	9412,	9452,	9491,	9530,   //-21~-12
	9569,	9609,	9648,	9687,	9726,	9765,	9804,	9844,	9883,	9922,   //-11~-2
	9961,	10000,	10039,	10078,	10117,	10156,	10195,	10234,	10273,	10312,  //-1~8
	10351,	10390,	10429,	10468,	10507,	10546,	10585,	10624,	10663,	10702,  //9-18
	10740,	10779,	10818,	10857,	10896,	10935,	10973,	11012,	11051,	11090,  //19-28
	11129,	11167,	11206,	11245,	11283,	11322,	11361,	11400,	11438,	11477,  //29-38
	11515,	11554,	11593,	11631,	11670,	11708,	11747,	11786,	11824,	11863,  //39-48
	11901,	11940,	11978,	12017,	12055,	12094,	12132,	12171,	12209,	12247,  //49-58
	12286,	12324,	12363,	12401,	12439,	12478,	12516,	12554,	12593,	12631,  //59-68
	12669,	12708,	12746,	12784,	12822,	12861,	12899,	12937,	12975,	13013,  //69-78
	13052,	13090,	13128,	13166,	13204,	13242,	13280,	13318,	13357,	13395,  //79-88
	13433,	13471,	13509,	13547,	13585,	13623,	13661,	13699,	13737,	13775,  //89-98
	13813,	13851,	13888,	13926,	13964,	14002,	14040,	14078,	14116,	14154,  //99-108
	14191,	14229,	14267,	14305,	14343,	14380,	14418,	14456,	14494,	14531,  //109-118
	14569,	14607,	14644,	14682,	14720,	14757,	14795,	14833,	14870,	14908,  //119-128
	14946,	14983,	15021,	15058,	15096,	15133,	15171,	15208,	15246,	15283,  //129-138
	15321,	15358,	15396,	15433,	15471,	15508,	15546,	15583,	15620,	15658,  //139-148
	15695,	15733,	15770
};

static const int temp_table_Cu50[203] = {
	39026, 																	// -51
	39242, 39458, 39674, 39890, 40106, 40322, 40537, 40753, 40969, 41184,	// -50~-41
	41400, 41616, 41831, 42047, 42262, 42478, 42693, 42909, 43124, 43349,	// -40~-31
	43555, 43770, 43985, 44200, 44416, 44631, 44846, 45061, 45276, 45491,	// -30~-21
	45706, 45921, 46136, 46351, 46566, 46780, 46995, 47210, 47425, 47639,	// -20~-11
	47854, 48069, 48284, 48498, 48713, 48927, 49142, 49356, 49571, 49786,	// -10~-1
	50000, 50214, 50429, 50643, 50858, 51072, 51286, 51501, 51715, 51929,	// 0~9
	52144, 52358, 52572, 52786, 53000, 53215, 53429, 53643, 53857, 54071,	// 10-19
	54285, 54500, 54714, 54928, 55142, 55356, 55570, 55784, 55998, 56212, 	// 20-29
	56426, 56640, 56854, 57068, 57282, 57496, 57710, 57924, 58137, 58351,	// 30-39
	58565, 58779, 58993, 59207, 59421, 59635, 59848, 60062, 60276, 60490,	// 40-49
	60704, 60918, 61132, 61345, 61559, 61773, 61987, 62201, 62415, 62628,	// 50-59
	62842, 63056, 63270, 63484, 63698, 63911, 64125, 64339, 64553, 64767,	// 60-69
	64981, 65194, 65408, 65622, 65836, 66050, 66264, 66478, 66692, 66906,	// 70-79
	67120, 67333, 67547, 67761, 67975, 68189, 68403, 68617, 68831, 69045,	// 80-89
	69259, 69473, 69687, 69901, 70115, 70329, 70544, 70762, 70972, 71186,	// 90-99
	71400, 71614, 71828, 72042, 72257, 72471, 72685, 72899, 73114, 73328,	// 100-109
	73542, 73751, 73971, 74185, 74400, 74614, 74828, 75043, 75258, 75477,	// 110-119
	75686, 75901, 76115, 76330, 76545, 76759, 76974, 77189, 77404, 77618,	// 120-129
	77833, 78048, 78263, 78477, 78692, 78907, 79122, 79337, 79552, 79767,	// 130-139
	79982, 80197, 80412, 80627, 80843, 81058, 81272, 81488, 81704, 81919,	// 140-149
	82134, 82349															// 150-151
};

static const int temp_table_Cu53[203] = { 
    4152, 															// -51
    4174, 4196, 4219, 4241, 4264, 4286, 4309, 4331, 4354, 4376,		// -50~-41
    4399, 4422, 4444, 4467, 4489, 4512, 4534, 4557, 4579, 4602,		// -40~-31
    4624, 4647, 4669, 4692, 4714, 4737, 4759, 4782, 4804, 4827,		// -30~-21
    4850, 4872, 4895, 4917, 4940, 4962, 4985, 5007, 5030, 5052,		// -20~-11
    5075, 5097, 5120, 5142, 5165, 5187, 5210, 5232, 5255, 5277,		// -10~-1
    5300, 5323, 5345, 5368, 5390, 5413, 5435, 5458, 5480, 5503,		// 0~9
    5525, 5548, 5570, 5593, 5615, 5638, 5660, 5683, 5705, 5728,		// 10-19  
    5750, 5773, 5796, 5818, 5841, 5863, 5886, 5908, 5931, 5953,		// 20-29  
    5975, 5998, 6021, 6043, 6066, 6088, 6111, 6133, 6156, 6178,		// 30-39		 
    6201, 6224, 6246, 6269, 6291, 6314, 6336, 6359, 6381, 6404,		// 40-49 
    6426, 6449, 6471, 6494, 6516, 6539, 6561, 6584, 6606, 6629,		// 50-59 
    6652, 6674, 6697, 6719, 6742, 6764, 6787, 6809, 6832, 6854,		// 60-69 
    6877, 6899, 6922, 6944, 6967, 6989, 7012, 7034, 7057, 7079,		// 70-79 
    7102, 7125, 7147, 7170, 7192, 7215, 7237, 7260, 7282, 7305,		// 80-89  
    7327, 7350, 7372, 7395, 7417, 7440, 7462, 7485, 7507, 7530,		// 90-99  
    7552, 7575, 7598, 7620, 7643, 7665, 7688, 7710, 7733, 7755,		// 100-109    
    7778, 7800, 7823, 7845, 7868, 7890, 7913, 7935, 7958, 7980,		// 110-119  
    8003, 8026, 8048, 8071, 8093, 8116, 8138, 8161, 8183, 8206,		// 120-129 
    8228, 8251, 8273, 8296, 8318, 8341, 8363, 8386, 8408, 8431,		// 130-139 
    8454, 8476, 8499, 8521, 8544, 8566, 8589, 8611, 8634, 8656,		// 140-149 
    8679, 8702 														// 150-151
 };

/****************************************************************************
* 函数名称: int32_t Pt100GetTemp(float resist)
* 功能描述: 根据电阻值计算PT100温度
* 输入参数: resist - 电阻值（单位为欧姆）
* 输出参数: 无
* 返 回 值: 返回计算得到的温度值（单位为0.01℃）
* 修改历史:
* 修改人:
* 修改日期:
* 修改内容:
****************************************************************************/
int32_t   Pt100GetTemp(float  resist)
{
    int resist_temporary_value;
    int i;
    int offset,scale;
    int iTmpV;
    float tmpV =0.0;
    float tmpV2;

    resist_temporary_value = (int)(resist*100);         // 将电阻值转换为整数形式（乘以100）
    
    if(resist_temporary_value < 7991)                   // 检查电阻值是否小于最小值
    {
        iTmpV = 99999;
        return iTmpV;
    }
    else if(resist_temporary_value > 15770)             // 检查电阻值是否大于最大值
    {
        iTmpV = 99999;
        return iTmpV;
    }
    else
    {
        i = 0;
        while((resist_temporary_value > temp_table_Pt100[i]) && (i <= 201))
        {
            i++;
        }
        {
            offset = resist_temporary_value - temp_table_Pt100[i - 1];      // 计算偏移量
            scale = temp_table_Pt100[i] -  temp_table_Pt100[i - 1];         // 计算比例
            tmpV = ((float)offset) / (float)(scale);                        // 计算温度值
            tmpV *= 100;
            offset = (uint32_t)tmpV;
            iTmpV = offset + ((i-1) + (-51)) * 100;                         // 计算最终温度值
        }
    }
    return iTmpV;
}

/****************************************************************************
 * 函数名称: int calcTempCu50(float resist)
 * 功能描述: 根据电阻值计算Cu50温度
 * 输入参数: resist - 电阻值（单位为欧姆）
 * 输出参数: 无
 * 返 回 值: 返回计算得到的温度值（单位为0.01℃）
 * 修改历史:
 * 修改人:
 * 修改日期:
 * 修改内容:
 ****************************************************************************/
int calcTempCu50(float resist )
{
	int resist_temporary_value;
    int i;
	int offset,scale;
	int iTmpV;
	float tmpV =0.0;
	float tmpV2;

	resist_temporary_value = (int)(resist*1000);
	
	if(resist_temporary_value < 39026)
	{
 		iTmpV = 99999;
		return iTmpV;
	}
	else if(resist_temporary_value > 82349)
	{
		iTmpV = 99999;
		return iTmpV;
	}
	else
	{
		i = 0;
		while((resist_temporary_value > temp_table_Cu50[i]) && (i <= 201))
		{
			i++;
		}
		{
			offset = resist_temporary_value - temp_table_Cu50[i - 1];
			scale = temp_table_Cu50[i] -  temp_table_Cu50[i - 1];
			tmpV = ((float)offset) / (float)(scale);
			tmpV *= 100;
			offset = (uint32_t)tmpV;
			iTmpV = offset + ((i-1) + (-51)) * 100;
		}
	}
	return iTmpV;
}

/****************************************************************************
 * 函数名称: int calcTempCu53(float resist)
 * 功能描述: 根据电阻值计算Cu53温度
 * 输入参数: resist - 电阻值（单位为欧姆）
 * 输出参数: 无
 * 返 回 值: 返回计算得到的温度值（单位为0.01℃）
 * 修改历史:
 * 修改人:
 * 修改日期:
 * 修改内容:
 ****************************************************************************/
int calcTempCu53(float resist )
{
	int resist_temporary_value;
    int i;
	int offset,scale;
	int iTmpV;
	float tmpV =0.0;
	float tmpV2;

	resist_temporary_value = (int)(resist*100);
	
	if(resist_temporary_value < temp_table_Cu53[0])
	{
 		iTmpV = 99999;
		return iTmpV;
	}
	else if(resist_temporary_value > temp_table_Cu53[202])
	{
		iTmpV = 99999;
		return iTmpV;
	}
	else
	{
		i = 0;
		while((resist_temporary_value > temp_table_Cu53[i]) && (i <= 201))
		{
			i++;
		}
		{
			offset = resist_temporary_value - temp_table_Cu53[i - 1];
			scale = temp_table_Cu53[i] -  temp_table_Cu53[i - 1];
			tmpV = ((float)offset) / (float)(scale);
			tmpV *= 100;
			offset = (uint32_t)tmpV;
			iTmpV = offset + ((i-1) + (-51)) * 100;
		}
	}
	return iTmpV;
}
