#include "mod_nonElec.h"
/*非电量保护*/

void runModNonElec(s_modNonElec* dp)
{	
	//跳闸
	dp->flg_nonElec1_trip = dp->setSwit_nonElec1_trip & (*dp->flg_nonElec1_kr);
	dp->flg_nonElec2_trip = dp->setSwit_nonElec2_trip & (*dp->flg_nonElec2_kr);
	dp->trip_nonElec_1 = delayRelay(dp->flg_nonElec1_trip, &dp->timebuf_trip_1,dp->set_t_nonElec_1);
	dp->trip_nonElec_2 = delayRelay(dp->flg_nonElec2_trip, &dp->timebuf_trip_2,dp->set_t_nonElec_2);
	return;
}
int initModNonElec(s_modNonElec* dp,char* dpName)
{
	dp->dpName = dpName;
	//注册开关量
	regBI(dpName, "trip_nonElec_1", 0, &dp->trip_nonElec_1);
	regBI(dpName, "trip_nonElec_2", 0, &dp->trip_nonElec_2);
	
	//注册启动元件
	regQdElement(&dp->flg_nonElec1_trip);
	regQdElement(&dp->flg_nonElec2_trip);
	
	//注册动作元件
	regTripElement(&dp->trip_nonElec_1,&dp->trip_matrix1);
	regTripElement(&dp->trip_nonElec_2,&dp->trip_matrix1);
	
	//加入队列
	addTaskLevel2(runModNonElec, dp);
	return SUCC;
}
int initModParmNonElec(s_modNonElec* dp)
{
	dp->setSwit_nonElec1_trip = getSetByDesc( "setSwit_nonElec1_trip");
	dp->setSwit_nonElec2_trip = getSetByDesc("setSwit_nonElec2_trip");
	dp->set_t_nonElec_1 = getSetByDesc("set_t_nonElec_1");
	dp->set_t_nonElec_2 = getSetByDesc("set_t_nonElec_2");
	
	dp->trip_matrix1 = getSetByDesc("trip_matrix_nonElec_1");
	dp->trip_matrix2 = getSetByDesc("trip_matrix_nonElec_2");
	
	return SUCC;
}