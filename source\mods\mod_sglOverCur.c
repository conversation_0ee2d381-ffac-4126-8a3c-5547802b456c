#include "mod_sglOverCur.h"
//零序过流
void runModSglOverCur(s_modSglOverCur* dp)
{
	int* i0;
	int* i0_half;
	
	//启动
    i0 = dp->i_30;
    i0_half = dp->i_30_half;
	
	overRelay(&dp->flg_sgl_oc_qd_val,i0,dp->set_i_sgl_oc * 0.95,&(dp->timebuf_qd2),1.0,1);	//启动逻辑
	overRelay(&dp->flg_sgl_oc_op_val2,i0,dp->set_i_sgl_oc,&(dp->timebuf_qd),0.95,1);

	//零序电流系数
	overRelay(&dp->flg_sgl_oc_op_val,i0,dp->set_i_sgl_oc,&dp->timebuf_op,0.95,1);
	//告警

	//动作
	dp->qd_sgl_oc = dp->flg_sgl_oc_qd_val & dp->setSwit_sgl_oc;
	dp->op_sgl_oc = delayRelay(dp->flg_sgl_oc_op_val & dp->flg_sgl_oc_op_val2 & dp->setSwit_sgl_oc,&(dp->timebuf_dly),dp->set_t_sgl_oc);

}
int initModSglOverCur(s_modSglOverCur* dp,char* dpName)
{
	dp->dpName = dpName;
	
	//注册开关量
	regBI(dpName,"op_sgl_oc",0,&dp->op_sgl_oc); //动作信号
	//regBI(dpName,"ovbi_sgl_oc",0,&dp->ovbi_sgl_oc);
	//注册模拟量
	
	//注册告警信息

	//注册启动元件
	regQdElement(&dp->qd_sgl_oc);
	
	//注册动作元件
	regTripElement(&dp->op_sgl_oc,&dp->trip_matrix);
	
	//加入队列
	addTaskLevel2(runModSglOverCur,dp);
	return SUCC;
}
int initModParmSglOverCur(s_modSglOverCur* dp)
{
	//软压板
	//dp->vbi_sgl_oc = getSetByDesc("vbi_sgl_oc");
	//dp->ovbi_sgl_oc = dp->vbi_sgl_oc;
	//控制字
	dp->setSwit_sgl_oc = mod_getSetByDesc(dp->dpName,"setSwit_sgl_oc");		//控制字
	//dp->setSwit_sgl_i_zc = getSetByDesc("setSwit_sgl_i_zc");
	
	//过流定值
	dp->set_i_sgl_oc = mod_getSetByDesc(dp->dpName,"set_i_sgl_oc");
	
	//零序过流时间
	dp->set_t_sgl_oc = mod_getSetByDesc(dp->dpName,"set_t_sgl_oc");
	dp->trip_matrix = mod_getSetByDesc(dp->dpName,"trip_matrix_sgl_oc");
	
	return SUCC;
	
}
