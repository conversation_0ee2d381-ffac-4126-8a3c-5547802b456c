/*
Generated by LwipMibCompiler
*/

#ifndef LWIP_HDR_APPS_SNMP_USER_BASED_SM_MIB_H
#define LWIP_HDR_APPS_SNMP_USER_BASED_SM_MIB_H

#include "lwip/apps/snmp_opts.h"
#if LWIP_SNMP

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#include "lwip/apps/snmp_core.h"

extern const struct snmp_mib snmpusmmib;

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* LWIP_SNMP */
#endif /* LWIP_HDR_APPS_SNMP_USER_BASED_SM_MIB_H */
