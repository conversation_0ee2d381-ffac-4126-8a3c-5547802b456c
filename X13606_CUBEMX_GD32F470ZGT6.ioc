#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART3_RX
Dma.Request1=USART6_RX
Dma.Request2=UART7_RX
Dma.Request3=USART2_RX
Dma.RequestsNb=4
Dma.UART7_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART7_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART7_RX.2.Instance=DMA1_Stream3
Dma.UART7_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART7_RX.2.MemInc=DMA_MINC_ENABLE
Dma.UART7_RX.2.Mode=DMA_NORMAL
Dma.UART7_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART7_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.UART7_RX.2.Priority=DMA_PRIORITY_LOW
Dma.UART7_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.3.Instance=DMA1_Stream5
Dma.USART2_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.3.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.3.Mode=DMA_NORMAL
Dma.USART2_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.3.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.0.Instance=DMA1_Stream1
Dma.USART3_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.0.Mode=DMA_NORMAL
Dma.USART3_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART6_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART6_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART6_RX.1.Instance=DMA2_Stream1
Dma.USART6_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART6_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART6_RX.1.Mode=DMA_NORMAL
Dma.USART6_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART6_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART6_RX.1.Priority=DMA_PRIORITY_LOW
Dma.USART6_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
ETH.IPParameters=MediaInterface,RxMode,MACAddr
ETH.MACAddr=00\:80\:E1\:25\:06\:19
ETH.MediaInterface=HAL_ETH_RMII_MODE
ETH.RxMode=ETH_RXPOLLING_MODE
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
LWIP.BSP.number=1
LWIP.GATEWAY_ADDRESS=***************
LWIP.IPParameters=LWIP_NETIF_STATUS_CALLBACK,LWIP_DHCP,IP_ADDRESS,NETMASK_ADDRESS,GATEWAY_ADDRESS
LWIP.IP_ADDRESS=***************
LWIP.LWIP_DHCP=0
LWIP.LWIP_NETIF_STATUS_CALLBACK=1
LWIP.NETMASK_ADDRESS=***************
LWIP.Version=v2.1.2_Cube
LWIP0.BSP.STBoard=false
LWIP0.BSP.api=BSP_COMPONENT_DRIVER
LWIP0.BSP.component=DP83848
LWIP0.BSP.condition=
LWIP0.BSP.instance=DP83848
LWIP0.BSP.ip=
LWIP0.BSP.mode=
LWIP0.BSP.name=Driver_PHY
LWIP0.BSP.semaphore=S_DP83848
LWIP0.BSP.solution=DP83848
Mcu.CPN=STM32F429ZGT6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=ETH
Mcu.IP10=TIM1
Mcu.IP11=TIM2
Mcu.IP12=TIM4
Mcu.IP13=TIM5
Mcu.IP14=UART7
Mcu.IP15=USART2
Mcu.IP16=USART3
Mcu.IP17=USART6
Mcu.IP2=LWIP
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=RTC
Mcu.IP6=SPI1
Mcu.IP7=SPI2
Mcu.IP8=SPI3
Mcu.IP9=SYS
Mcu.IPNb=18
Mcu.Name=STM32F429Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PC14/OSC32_IN
Mcu.Pin1=PC15/OSC32_OUT
Mcu.Pin10=PE7
Mcu.Pin11=PE8
Mcu.Pin12=PB10
Mcu.Pin13=PB11
Mcu.Pin14=PB12
Mcu.Pin15=PB13
Mcu.Pin16=PB14
Mcu.Pin17=PB15
Mcu.Pin18=PD8
Mcu.Pin19=PD9
Mcu.Pin2=PH0/OSC_IN
Mcu.Pin20=PC6
Mcu.Pin21=PC7
Mcu.Pin22=PA9
Mcu.Pin23=PA10
Mcu.Pin24=PA13
Mcu.Pin25=PA14
Mcu.Pin26=PC10
Mcu.Pin27=PC11
Mcu.Pin28=PC12
Mcu.Pin29=PD3
Mcu.Pin3=PH1/OSC_OUT
Mcu.Pin30=PD5
Mcu.Pin31=PD6
Mcu.Pin32=PB3
Mcu.Pin33=PB4
Mcu.Pin34=PB5
Mcu.Pin35=VP_LWIP_VS_Enabled
Mcu.Pin36=VP_RTC_VS_RTC_Activate
Mcu.Pin37=VP_RTC_VS_RTC_Calendar
Mcu.Pin38=VP_SYS_VS_Systick
Mcu.Pin39=VP_TIM1_VS_ClockSourceINT
Mcu.Pin4=PC1
Mcu.Pin40=VP_TIM2_VS_ClockSourceINT
Mcu.Pin41=VP_TIM4_VS_ClockSourceINT
Mcu.Pin42=VP_TIM5_VS_ClockSourceINT
Mcu.Pin5=PA1
Mcu.Pin6=PA2
Mcu.Pin7=PA7
Mcu.Pin8=PC4
Mcu.Pin9=PC5
Mcu.PinsNb=43
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F429ZGTx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:6\:0\:true\:false\:true\:false\:true\:true
NVIC.DMA1_Stream3_IRQn=true\:6\:0\:true\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:6\:0\:true\:false\:true\:false\:true\:true
NVIC.DMA2_Stream1_IRQn=true\:6\:0\:true\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ETH_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.ETH_WKUP_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SPI1_IRQn=true\:7\:0\:true\:false\:true\:true\:true\:true
NVIC.SPI2_IRQn=true\:7\:0\:true\:false\:true\:true\:true\:true
NVIC.SPI3_IRQn=true\:7\:0\:true\:false\:true\:true\:true\:true
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.TIM1_BRK_TIM9_IRQn=true\:2\:0\:true\:false\:true\:true\:true\:true
NVIC.TIM1_CC_IRQn=true\:2\:0\:true\:false\:true\:true\:true\:true
NVIC.TIM1_TRG_COM_TIM11_IRQn=true\:2\:0\:true\:false\:true\:true\:true\:true
NVIC.TIM1_UP_TIM10_IRQn=true\:2\:0\:true\:false\:true\:true\:true\:true
NVIC.TIM2_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.TIM4_IRQn=true\:4\:0\:true\:false\:true\:true\:true\:true
NVIC.TIM5_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UART7_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA1.Mode=RMII
PA1.Signal=ETH_REF_CLK
PA10.Locked=true
PA10.Signal=S_TIM1_CH3
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=RMII
PA2.Signal=ETH_MDIO
PA7.Mode=RMII
PA7.Signal=ETH_CRS_DV
PA9.Locked=true
PA9.Signal=S_TIM1_CH2
PB10.Locked=true
PB10.Signal=ETH_RX_ER
PB11.Mode=RMII
PB11.Signal=ETH_TX_EN
PB12.Mode=RMII
PB12.Signal=ETH_TXD0
PB13.Mode=RMII
PB13.Signal=ETH_TXD1
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB3.Locked=true
PB3.Mode=Full_Duplex_Master
PB3.Signal=SPI1_SCK
PB4.Locked=true
PB4.Mode=Full_Duplex_Master
PB4.Signal=SPI1_MISO
PB5.Mode=Full_Duplex_Master
PB5.Signal=SPI1_MOSI
PC1.Mode=RMII
PC1.Signal=ETH_MDC
PC10.Mode=Full_Duplex_Master
PC10.Signal=SPI3_SCK
PC11.Mode=Full_Duplex_Master
PC11.Signal=SPI3_MISO
PC12.Mode=Full_Duplex_Master
PC12.Signal=SPI3_MOSI
PC14/OSC32_IN.Mode=LSE-External-Oscillator
PC14/OSC32_IN.Signal=RCC_OSC32_IN
PC15/OSC32_OUT.Mode=LSE-External-Oscillator
PC15/OSC32_OUT.Signal=RCC_OSC32_OUT
PC4.Mode=RMII
PC4.Signal=ETH_RXD0
PC5.Mode=RMII
PC5.Signal=ETH_RXD1
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PD3.Locked=true
PD3.Mode=Full_Duplex_Master
PD3.Signal=SPI2_SCK
PD5.Mode=Asynchronous
PD5.Signal=USART2_TX
PD6.Locked=true
PD6.Mode=Asynchronous
PD6.Signal=USART2_RX
PD8.Locked=true
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.Locked=true
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE7.Locked=true
PE7.Mode=Asynchronous
PE7.Signal=UART7_RX
PE8.Locked=true
PE8.Mode=Asynchronous
PE8.Signal=UART7_TX
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F429ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=X13606_CUBEMX_GD32F470ZGT6.ioc
ProjectManager.ProjectName=X13606_CUBEMX_GD32F470ZGT6
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_TIM2_Init-TIM2-false-HAL-true,5-MX_RTC_Init-RTC-false-HAL-true,6-MX_SPI1_Init-SPI1-false-HAL-true,7-MX_SPI2_Init-SPI2-false-HAL-true,8-MX_SPI3_Init-SPI3-false-HAL-true,9-MX_TIM4_Init-TIM4-false-HAL-true,10-MX_TIM5_Init-TIM5-false-HAL-true,11-MX_UART7_Init-UART7-false-HAL-true,12-MX_USART3_UART_Init-USART3-false-HAL-true,13-MX_USART6_UART_Init-USART6-false-HAL-true,14-MX_LWIP_Init-LWIP-false-HAL-false,15-MX_TIM1_Init-TIM1-false-HAL-true,16-MX_USART2_UART_Init-USART2-false-HAL-true
RCC.48MHZClocksFreq_Value=90000000
RCC.AHBFreq_Value=180000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=45000000
RCC.APB1TimFreq_Value=90000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=90000000
RCC.APB2TimFreq_Value=180000000
RCC.CortexFreq_Value=180000000
RCC.EthernetFreq_Value=180000000
RCC.FCLKCortexFreq_Value=180000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=180000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=160000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LCDTFTFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RCC_RTC_Clock_Source,RCC_RTC_Clock_SourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SAI_AClocksFreq_Value,SAI_BClocksFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value,VCOSAIOutputFreq_ValueQ,VCOSAIOutputFreq_ValueR,VcooutputI2S,VcooutputI2SQ
RCC.LCDTFTFreq_Value=20416666.666666668
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=180000000
RCC.PLLCLKFreq_Value=180000000
RCC.PLLM=15
RCC.PLLN=216
RCC.PLLQCLKFreq_Value=90000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RCC_RTC_Clock_Source=RCC_RTCCLKSOURCE_LSE
RCC.RCC_RTC_Clock_SourceVirtual=RCC_RTCCLKSOURCE_LSE
RCC.RTCFreq_Value=32768
RCC.RTCHSEDivFreq_Value=12500000
RCC.SAI_AClocksFreq_Value=20416666.666666668
RCC.SAI_BClocksFreq_Value=20416666.666666668
RCC.SYSCLKFreq_VALUE=180000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=320000000
RCC.VCOInputFreq_Value=1666666.6666666667
RCC.VCOOutputFreq_Value=360000000
RCC.VCOSAIOutputFreq_Value=81666666.66666667
RCC.VCOSAIOutputFreq_ValueQ=20416666.666666668
RCC.VCOSAIOutputFreq_ValueR=40833333.333333336
RCC.VcooutputI2S=160000000
RCC.VcooutputI2SQ=160000000
RTC.AsynchPrediv=31
RTC.Hours=15
RTC.IPParameters=AsynchPrediv,SynchPrediv,Hours,Minutes,Year
RTC.Minutes=18
RTC.SynchPrediv=1023
RTC.Year=50
SH.S_TIM1_CH2.0=TIM1_CH2,Input_Capture2_from_TI2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH3.0=TIM1_CH3,Input_Capture3_from_TI3
SH.S_TIM1_CH3.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_4
SPI1.CLKPolarity=SPI_POLARITY_HIGH
SPI1.CalculateBaudRate=22.5 MBits/s
SPI1.DataSize=SPI_DATASIZE_16BIT
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler,DataSize,CLKPolarity
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_4
SPI2.CLKPhase=SPI_PHASE_1EDGE
SPI2.CalculateBaudRate=11.25 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler,CLKPhase
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
SPI3.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_16
SPI3.CLKPhase=SPI_PHASE_2EDGE
SPI3.CalculateBaudRate=2.8125 MBits/s
SPI3.Direction=SPI_DIRECTION_2LINES
SPI3.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler,CLKPhase
SPI3.Mode=SPI_MODE_MASTER
SPI3.VirtualType=VM_MASTER
TIM1.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM1.Channel-Input_Capture2_from_TI2=TIM_CHANNEL_2
TIM1.Channel-Input_Capture3_from_TI3=TIM_CHANNEL_3
TIM1.IPParameters=Channel-Input_Capture2_from_TI2,Prescaler,Period,AutoReloadPreload,Channel-Input_Capture3_from_TI3
TIM1.Period=0xffff
TIM1.Prescaler=89
TIM2.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM2.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM2.Period=(10000 - 1)
TIM2.Prescaler=(9000/2000 - 1)
TIM2.TIM_MasterOutputTrigger=TIM_TRGO_RESET
TIM4.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM4.IPParameters=Prescaler,Period,AutoReloadPreload
TIM4.Period=(10000 - 1)
TIM4.Prescaler=(9000/200 - 1)
TIM5.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM5.IPParameters=Prescaler,Period,AutoReloadPreload
TIM5.Period=(10000 - 1)
TIM5.Prescaler=(9000/2000 - 1)
UART7.IPParameters=VirtualMode
UART7.VirtualMode=Asynchronous
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
USART6.IPParameters=VirtualMode
USART6.VirtualMode=VM_ASYNC
VP_LWIP_VS_Enabled.Mode=Enabled
VP_LWIP_VS_Enabled.Signal=LWIP_VS_Enabled
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM4_VS_ClockSourceINT.Mode=Internal
VP_TIM4_VS_ClockSourceINT.Signal=TIM4_VS_ClockSourceINT
VP_TIM5_VS_ClockSourceINT.Mode=Internal
VP_TIM5_VS_ClockSourceINT.Signal=TIM5_VS_ClockSourceINT
board=custom
