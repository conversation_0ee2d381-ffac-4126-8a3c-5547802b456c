#ifndef		__S7KMSEND_H__
#define		__S7KMSEND_H__

#include	"s7kmCommon.h"
#include	"excitation_def.h"

uint8 s7kmSendData(uint8 *sendBuff, uint16 sendLen, uint8 boardNo);

void  s7kmCallVersion(void);
//s7kmMakeDataFrame
void  call_analogdata(USHORT m_starpos, USHORT m_len, UCHAR m_BoardNo);
//s7kmMakeDataFrame
void  call_switchdata(USHORT m_starpos, USHORT m_len, UCHAR m_BoardNo);
//s7kmMakeDataFrame

void  call_settingsdata(CALL_SETTINGS *__pcall);
//s7kmMakeDataFrame2
CHAR  send_settings(CALL_SETTINGS *__pcall);
//s7kmMakeDataFrame2
UCHAR send_control(DEF_CONTROL *__pctrl, UCHAR m_BoardNo);
//s7kmMakeDataFrame2
UCHAR send_outport(USHORT m_point, UCHAR m_BoardNo);
//s7kmMakeDataFrame


#endif		//__S7KMSEND_H__
