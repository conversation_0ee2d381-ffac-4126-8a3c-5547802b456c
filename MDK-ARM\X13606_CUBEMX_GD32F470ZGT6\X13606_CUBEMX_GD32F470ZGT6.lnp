--cpu=Cortex-M4.fp.sp
"x13606_cubemx_gd32f470zgt6\startup_stm32f429xx.o"
"x13606_cubemx_gd32f470zgt6\main.o"
"x13606_cubemx_gd32f470zgt6\gpio.o"
"x13606_cubemx_gd32f470zgt6\dma.o"
"x13606_cubemx_gd32f470zgt6\lwip.o"
"x13606_cubemx_gd32f470zgt6\ethernetif.o"
"x13606_cubemx_gd32f470zgt6\rtc.o"
"x13606_cubemx_gd32f470zgt6\spi.o"
"x13606_cubemx_gd32f470zgt6\tim.o"
"x13606_cubemx_gd32f470zgt6\usart.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_it.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_msp.o"
"x13606_cubemx_gd32f470zgt6\dp83848.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_rcc.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_rcc_ex.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_flash.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_flash_ex.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_flash_ramfunc.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_gpio.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_dma_ex.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_dma.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_pwr.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_pwr_ex.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_cortex.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_exti.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_eth.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_rtc.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_rtc_ex.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_spi.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_tim.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_tim_ex.o"
"x13606_cubemx_gd32f470zgt6\stm32f4xx_hal_uart.o"
"x13606_cubemx_gd32f470zgt6\system_stm32f4xx.o"
"x13606_cubemx_gd32f470zgt6\auth.o"
"x13606_cubemx_gd32f470zgt6\ccp.o"
"x13606_cubemx_gd32f470zgt6\chap_ms.o"
"x13606_cubemx_gd32f470zgt6\chap-md5.o"
"x13606_cubemx_gd32f470zgt6\chap-new.o"
"x13606_cubemx_gd32f470zgt6\demand.o"
"x13606_cubemx_gd32f470zgt6\eap.o"
"x13606_cubemx_gd32f470zgt6\eui64.o"
"x13606_cubemx_gd32f470zgt6\fsm.o"
"x13606_cubemx_gd32f470zgt6\ipcp.o"
"x13606_cubemx_gd32f470zgt6\ipv6cp.o"
"x13606_cubemx_gd32f470zgt6\lcp.o"
"x13606_cubemx_gd32f470zgt6\magic.o"
"x13606_cubemx_gd32f470zgt6\mppe.o"
"x13606_cubemx_gd32f470zgt6\multilink.o"
"x13606_cubemx_gd32f470zgt6\ppp.o"
"x13606_cubemx_gd32f470zgt6\pppapi.o"
"x13606_cubemx_gd32f470zgt6\pppcrypt.o"
"x13606_cubemx_gd32f470zgt6\pppoe.o"
"x13606_cubemx_gd32f470zgt6\pppol2tp.o"
"x13606_cubemx_gd32f470zgt6\pppos.o"
"x13606_cubemx_gd32f470zgt6\upap.o"
"x13606_cubemx_gd32f470zgt6\utils.o"
"x13606_cubemx_gd32f470zgt6\vj.o"
"x13606_cubemx_gd32f470zgt6\bridgeif.o"
"x13606_cubemx_gd32f470zgt6\bridgeif_fdb.o"
"x13606_cubemx_gd32f470zgt6\ethernet.o"
"x13606_cubemx_gd32f470zgt6\lowpan6.o"
"x13606_cubemx_gd32f470zgt6\lowpan6_ble.o"
"x13606_cubemx_gd32f470zgt6\lowpan6_common.o"
"x13606_cubemx_gd32f470zgt6\slipif.o"
"x13606_cubemx_gd32f470zgt6\zepif.o"
"x13606_cubemx_gd32f470zgt6\ecp.o"
"x13606_cubemx_gd32f470zgt6\api_lib.o"
"x13606_cubemx_gd32f470zgt6\api_msg.o"
"x13606_cubemx_gd32f470zgt6\err.o"
"x13606_cubemx_gd32f470zgt6\if_api.o"
"x13606_cubemx_gd32f470zgt6\netbuf.o"
"x13606_cubemx_gd32f470zgt6\netdb.o"
"x13606_cubemx_gd32f470zgt6\netifapi.o"
"x13606_cubemx_gd32f470zgt6\sockets.o"
"x13606_cubemx_gd32f470zgt6\tcpip.o"
"x13606_cubemx_gd32f470zgt6\altcp.o"
"x13606_cubemx_gd32f470zgt6\altcp_alloc.o"
"x13606_cubemx_gd32f470zgt6\altcp_tcp.o"
"x13606_cubemx_gd32f470zgt6\def.o"
"x13606_cubemx_gd32f470zgt6\dns.o"
"x13606_cubemx_gd32f470zgt6\inet_chksum.o"
"x13606_cubemx_gd32f470zgt6\init.o"
"x13606_cubemx_gd32f470zgt6\ip.o"
"x13606_cubemx_gd32f470zgt6\mem.o"
"x13606_cubemx_gd32f470zgt6\memp.o"
"x13606_cubemx_gd32f470zgt6\netif.o"
"x13606_cubemx_gd32f470zgt6\pbuf.o"
"x13606_cubemx_gd32f470zgt6\raw.o"
"x13606_cubemx_gd32f470zgt6\stats.o"
"x13606_cubemx_gd32f470zgt6\sys.o"
"x13606_cubemx_gd32f470zgt6\tcp.o"
"x13606_cubemx_gd32f470zgt6\tcp_in.o"
"x13606_cubemx_gd32f470zgt6\tcp_out.o"
"x13606_cubemx_gd32f470zgt6\timeouts.o"
"x13606_cubemx_gd32f470zgt6\udp.o"
"x13606_cubemx_gd32f470zgt6\autoip.o"
"x13606_cubemx_gd32f470zgt6\dhcp.o"
"x13606_cubemx_gd32f470zgt6\etharp.o"
"x13606_cubemx_gd32f470zgt6\icmp.o"
"x13606_cubemx_gd32f470zgt6\igmp.o"
"x13606_cubemx_gd32f470zgt6\ip4.o"
"x13606_cubemx_gd32f470zgt6\ip4_addr.o"
"x13606_cubemx_gd32f470zgt6\ip4_frag.o"
"x13606_cubemx_gd32f470zgt6\dhcp6.o"
"x13606_cubemx_gd32f470zgt6\ethip6.o"
"x13606_cubemx_gd32f470zgt6\icmp6.o"
"x13606_cubemx_gd32f470zgt6\inet6.o"
"x13606_cubemx_gd32f470zgt6\ip6.o"
"x13606_cubemx_gd32f470zgt6\ip6_addr.o"
"x13606_cubemx_gd32f470zgt6\ip6_frag.o"
"x13606_cubemx_gd32f470zgt6\mld6.o"
"x13606_cubemx_gd32f470zgt6\nd6.o"
"x13606_cubemx_gd32f470zgt6\mqtt.o"
"x13606_cubemx_gd32f470zgt6\xsj_lib.o"
"x13606_cubemx_gd32f470zgt6\gpio_xsj.o"
"x13606_cubemx_gd32f470zgt6\uart.o"
"x13606_cubemx_gd32f470zgt6\spi_xsj.o"
"x13606_cubemx_gd32f470zgt6\fm25v02.o"
"x13606_cubemx_gd32f470zgt6\w25qxx.o"
"x13606_cubemx_gd32f470zgt6\tcp_client.o"
"x13606_cubemx_gd32f470zgt6\tcp_server.o"
"x13606_cubemx_gd32f470zgt6\udp_client.o"
"x13606_cubemx_gd32f470zgt6\udp_server.o"
"x13606_cubemx_gd32f470zgt6\serial7000master.o"
"x13606_cubemx_gd32f470zgt6\excitation.o"
"x13606_cubemx_gd32f470zgt6\s7kmcommon.o"
"x13606_cubemx_gd32f470zgt6\s7kmsend.o"
"x13606_cubemx_gd32f470zgt6\s7kmreceive.o"
"x13606_cubemx_gd32f470zgt6\bsp_ad7616.o"
"x13606_cubemx_gd32f470zgt6\upgrade_flag.o"
"x13606_cubemx_gd32f470zgt6\tcp_plc_server.o"
"x13606_cubemx_gd32f470zgt6\ad7792.o"
"x13606_cubemx_gd32f470zgt6\temp_referable.o"
"x13606_cubemx_gd32f470zgt6\bcode_task.o"
"x13606_cubemx_gd32f470zgt6\n103app.o"
"x13606_cubemx_gd32f470zgt6\n103link.o"
"x13606_cubemx_gd32f470zgt6\reftable103.o"
"x13606_cubemx_gd32f470zgt6\s103app.o"
"x13606_cubemx_gd32f470zgt6\s103link.o"
"x13606_cubemx_gd32f470zgt6\ana.o"
"x13606_cubemx_gd32f470zgt6\bi.o"
"x13606_cubemx_gd32f470zgt6\common.o"
"x13606_cubemx_gd32f470zgt6\control.o"
"x13606_cubemx_gd32f470zgt6\keepstatus.o"
"x13606_cubemx_gd32f470zgt6\setting.o"
"x13606_cubemx_gd32f470zgt6\task_queue.o"
"x13606_cubemx_gd32f470zgt6\lb_task.o"
"x13606_cubemx_gd32f470zgt6\eventreftable.o"
"x13606_cubemx_gd32f470zgt6\opreftable.o"
"x13606_cubemx_gd32f470zgt6\soe_task.o"
"x13606_cubemx_gd32f470zgt6\rtc_task.o"
"x13606_cubemx_gd32f470zgt6\sample_task.o"
"x13606_cubemx_gd32f470zgt6\modbus.o"
"x13606_cubemx_gd32f470zgt6\plc_comm.o"
"..\source\platform\plc_task\plc_lib.lib"
"x13606_cubemx_gd32f470zgt6\plc_reftable.o"
"x13606_cubemx_gd32f470zgt6\language.o"
"x13606_cubemx_gd32f470zgt6\para_groups.o"
"x13606_cubemx_gd32f470zgt6\ref_tables.o"
"x13606_cubemx_gd32f470zgt6\fun.o"
"x13606_cubemx_gd32f470zgt6\appdevice.o"
"x13606_cubemx_gd32f470zgt6\mod_bi.o"
"x13606_cubemx_gd32f470zgt6\mod_bo.o"
"x13606_cubemx_gd32f470zgt6\mod_cur.o"
"x13606_cubemx_gd32f470zgt6\mod_getangle.o"
"x13606_cubemx_gd32f470zgt6\mod_measdata.o"
"x13606_cubemx_gd32f470zgt6\mod_overcur.o"
"x13606_cubemx_gd32f470zgt6\mod_rmtctl.o"
"x13606_cubemx_gd32f470zgt6\mod_selfchk.o"
"x13606_cubemx_gd32f470zgt6\mod_vol.o"
"x13606_cubemx_gd32f470zgt6\mod_ctfail.o"
"x13606_cubemx_gd32f470zgt6\mod_nonelec.o"
"x13606_cubemx_gd32f470zgt6\mod_novol.o"
"x13606_cubemx_gd32f470zgt6\mod_overload.o"
"x13606_cubemx_gd32f470zgt6\mod_overvol.o"
"x13606_cubemx_gd32f470zgt6\mod_ptfail.o"
"x13606_cubemx_gd32f470zgt6\mod_sglovercur.o"
"x13606_cubemx_gd32f470zgt6\mod_underfreq.o"
"x13606_cubemx_gd32f470zgt6\mod_general.o"
"x13606_cubemx_gd32f470zgt6\mod_negovercur.o"
"x13606_cubemx_gd32f470zgt6\mod_overfreq.o"
"x13606_cubemx_gd32f470zgt6\mod_overspeed.o"
"x13606_cubemx_gd32f470zgt6\mod_rvspwr.o"
"x13606_cubemx_gd32f470zgt6\mod_underexc.o"
"x13606_cubemx_gd32f470zgt6\mod_synctr.o"
"x13606_cubemx_gd32f470zgt6\umain.o"
"..\lib\arm_cortexM4lf_math.lib"
--library_type=microlib --strict --scatter "X13606_CUBEMX_GD32F470ZGT6\X13606_CUBEMX_GD32F470ZGT6.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\X13606_CUBEMX_GD32F470ZGT6.map" -o X13606_CUBEMX_GD32F470ZGT6\X13606_CUBEMX_GD32F470ZGT6.axf