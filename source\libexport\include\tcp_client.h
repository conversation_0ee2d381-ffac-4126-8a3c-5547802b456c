

#ifndef __TCP_CLIENT_H__
#define __TCP_CLIENT_H__


#ifdef __cplusplus
extern "C"{
#endif


#include "main.h"
#include "lwip.h"
#include "tcp.h"


err_t tcp_client_recv(void *arg, struct tcp_pcb *tpcb,
                             struct pbuf *p, err_t err);
    
static err_t tcp_client_connected(void *arg, struct tcp_pcb *tpcb, err_t err);

void tcp_client_init(void);


#ifdef __cplusplus
}
#endif

#endif
