

#include "xsj_lib.h"
#include "stdio.h"
#include <string.h>
#include "main.h"
#include "gpio_xsj.h"


//#if FLASH_TEST


//w25q64
void tflash_test(void)
{
    uint32_t i;
    uint8_t  ID[2];
    uint8_t  wData[0x100];
    uint8_t  rData[0x100];
	
    printf("\r\n SPI-W25Qxxx Example \r\n\r\n");

    /*##-1- Read the device ID  ########################*/
    external_memoryFLASH_init();
    external_memoryFLASH_Read_ID(ID);
    printf(" W25Qxxx ID is : 0x%02X 0x%02X \r\n\r\n",ID[0],ID[1]);  //0xef 0x16
    
    /*##-2- Erase Block ##################################*/
    if(external_memoryFLASH_Erase(0) == 0)
        printf(" SPI Erase Block ok\r\n");
    else
        <PERSON><PERSON><PERSON>_Handler();

    /*##-3- Written to the flash ########################*/
    /* fill buffer */
    for(i =0;i<0x100;i ++)
    {
        wData[i] = i;
        rData[i] = 0;
    }
    
    if(external_memoryFLASH_write(0x00, wData, 0x100) == 0)
        printf(" SPI Write ok\r\n");
    else
        Error_Handler();
	
    /*##-4- Read the flash     ########################*/
    if(external_memoryFLASH_read(0x00, rData, 0x100)== 0)
        printf(" SPI Read ok\r\n\r\n");
    else
        Error_Handler();

    printf("SPI Read Data : \r\n");
    for(i =0;i<0x100;i++)
       printf("0x%02X  ",rData[i]);
    printf("\r\n\r\n");
    
    /*##-5- check date          ########################*/   
    if(memcmp(wData,rData,0x100) == 0 )
        printf(" W25Q128FV SPI Test OK\r\n");
    else
        printf(" W25Q128FV SPI Test False\r\n");

		
		while(1)
		{
		    HAL_Delay(1000);
		    //HAL_GPIO_TogglePin(LED2_PIN_PORT, LED2_PIN_Pin);
		}
}


//#endif
