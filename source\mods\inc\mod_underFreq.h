#ifndef _MOD_UNDERFREQ_H_
#define _MOD_UNDERFREQ_H_

#include "task_queue.h"
#include "fun.h"

typedef struct
{
	char* dpName;
	
	//输入信号
	char* flg_abc;
	float* fm;
	int* u_max;
	
	//输出信号
	unsigned char qd_uf;
	unsigned char op_uf;
	unsigned char ovbi_uf;		//软压板
	
	//输出模拟量
	
	//定值
	unsigned char vbi_uf;		//软压板
	unsigned char setSwit_uf;	//控制字
	int set_f_uf;	//低频定值
	unsigned int set_t_uf;	//低频时间
	int trip_matrix;
	
	//内部使用
	unsigned char op_uf_pre;
	int timebuf_for_op;	//起动元件时间
	int timebuf_blk;
	int timebuf_op;
	int set_f_uf_qd;	//+0.05hz启动门槛
	unsigned char flg_blk;
	unsigned char flg_uf_op_val;
	
}s_modUnderFreq;

int initModUnderFreq(s_modUnderFreq* dp, char* dpName);
int initModParmUnderFreq(s_modUnderFreq* dp);


#endif