//exchangeData

#ifndef _EXCHANGE_DATA_H_
#define _EXCHANGE_DATA_H_

#define UINT8     unsigned char

typedef struct
{
    //UINT8   syn_ok; //握手标志
    UINT8   enbale;   //enbale; //使能
    UINT8   reset;    //reset;  //复位
    UINT8   block;    //block;  //闭锁
    UINT8   out;      //out;    //出口
    UINT8   sta;      //sta;    //状态
    UINT8   fal;      //fal;    //错误

}s_modPLCSyn;

//交互数据 初始化,共享数据modPLCSyn 清零
int initExchangeData(s_modPLCSyn* dp);

//PLC传值，供PLC调用
void plc_get_data( UINT8 enbale, UINT8 reset, UINT8 block,
                   UINT8* out,   UINT8* sta,  UINT8* fal );

#endif //_EXCHANGE_DATA_H_