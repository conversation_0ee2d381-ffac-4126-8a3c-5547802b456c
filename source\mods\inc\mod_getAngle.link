void linkModGetAngle(s_modGetAngle * dp)
{
	dp->vector1_array[0] =  &modCur.VECTOR_f[0*2];		//Ia
	dp->vector2_array[0] = 	&modCur.VECTOR_f[1*2];		//Ib
	
	dp->vector1_array[1] =  &modCur.VECTOR_f[1*2];		//Ib
	dp->vector2_array[1] = 	&modCur.VECTOR_f[2*2];		//Ic

	dp->vector1_array[2] =  &modCur.VECTOR_f[2*2];		//Ic
	dp->vector2_array[2] = 	&modCur.VECTOR_f[0*2];		//Ia
	
	dp->vector1_array[3] =  &modCur.VECTOR_f[3*2];		//I0
	dp->vector2_array[3] = 	&modCur.VECTOR_f[0*2];		//Ia
	
	dp->vector1_array[4] =  &modVol.VECTOR_f[3*2];		//U0
	dp->vector2_array[4] = 	&modVol.VECTOR_f[0*2];		//Ua
	
	dp->vector1_array[5] =  &modVol.VECTOR_f[4*2];		//Ux
	dp->vector2_array[5] = 	&modVol.VECTOR_f[0*2];		//Ua
	
	dp->vector1_array[6] =  &modVol.VECTOR_f[0*2];		//Ua
	dp->vector2_array[6] = 	&modVol.VECTOR_f[1*2];		//Ub
	
	dp->vector1_array[7] =  &modVol.VECTOR_f[1*2];		//Ub
	dp->vector2_array[7] = 	&modVol.VECTOR_f[2*2];		//Uc
	
	dp->vector1_array[8] =  &modVol.VECTOR_f[2*2];		//Uc
	dp->vector2_array[8] = 	&modVol.VECTOR_f[0*2];		//Ua
	
	dp->vector1_array[9] =  &modCur.VECTOR_f[3*2];		//I0
	dp->vector2_array[9] =  &modVol.VECTOR_f[8*2];		//3U0
	
	dp->vector_modVol = &modVol.VECTOR_f[0*2];         //Ua
    
	return;
}
