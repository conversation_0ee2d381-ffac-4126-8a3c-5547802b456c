#include "mod_negOverCur.h"
/*负序过流保护*/
void runModNegOverCur(s_modNegOverCur* dp)
{
	int* i2;
	
	//启动
	i2 = dp->i2;
	
	overRelay(&dp->flg_neg_oc_qd_val,i2,dp->set_i_neg_oc * 0.95,&(dp->timebuf_qd2),1.0,1);	//启动逻辑
	overRelay(&dp->flg_neg_oc_op_val2,i2,dp->set_i_neg_oc,&(dp->timebuf_qd),0.95,1);

	//负序电流系数
	overRelay(&dp->flg_neg_oc_op_val,i2,dp->set_i_neg_oc,&dp->timebuf_op,0.95,1);
	
	//动作
	dp->qd_neg_oc = dp->flg_neg_oc_qd_val & dp->setSwit_neg_oc;
	dp->op_neg_oc = delayRelay(dp->flg_neg_oc_op_val & dp->flg_neg_oc_op_val2 & dp->setSwit_neg_oc,&(dp->timebuf_dly),dp->set_t_neg_oc);
	
}
int initModNegOverCur(s_modNegOverCur* dp,char* dpName)
{
	dp->dpName = dpName;
	
	//注册开关量
	regBI(dpName,"op_neg_oc",0,&dp->op_neg_oc); //动作信号
	//regBI(dpName,"ovbi_neg_oc",0,&dp->ovbi_neg_oc);
	//注册模拟量
	
	//注册启动元件
	regQdElement(&dp->qd_neg_oc);
	
	//注册动作元件
	regTripElement(&dp->op_neg_oc,&dp->trip_matrix);
	
	//加入队列
	addTaskLevel2(runModNegOverCur,dp);
	return SUCC;
}
int initModParmNegOverCur(s_modNegOverCur* dp)
{
	//软压板
	//dp->vbi_neg_oc = getSetByDesc("vbi_neg_oc");
	//dp->ovbi_neg_oc = dp->vbi_neg_oc;
	//控制字
	dp->setSwit_neg_oc = mod_getSetByDesc(dp->dpName,"setSwit_neg_oc");		//控制字
	
	//过流定值
	dp->set_i_neg_oc = mod_getSetByDesc(dp->dpName,"set_i_neg_oc");
	
	//负序过流时间
	dp->set_t_neg_oc = mod_getSetByDesc(dp->dpName,"set_t_neg_oc");
	dp->trip_matrix = mod_getSetByDesc(dp->dpName,"trip_matrix_neg_oc");
	
	return SUCC;
	
}
