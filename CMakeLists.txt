cmake_minimum_required(VERSION 3.31)
project(X13606F470)

set(CMAKE_C_STANDARD 99)

add_definitions(
        -DUSE_HAL_DRIVER
        -DSTM32F429xx

        -DLWIP_NOASSERT
        -D__FPU_PRESENT=1U
        -DARM_MATH_CM4
        -D__CC_ARM
        -D__TARGET_FPU_VFP

#        -DDEBUG
        -UWIN32)

include_directories(Drivers/BSP/Components/dp83848)
include_directories(Drivers/CMSIS)
include_directories(Drivers/CMSIS/Device)
include_directories(Drivers/CMSIS/Device/ST)
include_directories(Drivers/CMSIS/Device/ST/STM32F4xx)
include_directories(Drivers/CMSIS/Device/ST/STM32F4xx/Include)
include_directories(Drivers/CMSIS/Include)
include_directories(Drivers/STM32F4xx_HAL_Driver/Inc)
include_directories(Drivers/STM32F4xx_HAL_Driver/Inc/Legacy)
include_directories(Inc)
include_directories(lib)
include_directories(MDK-ARM)
include_directories(MDK-ARM/RTE)
include_directories(MDK-ARM/RTE/_X13606_CUBEMX_GD32F470ZGT6)
include_directories(Middlewares/Third_Party/LwIP/src/include)
include_directories(Middlewares/Third_Party/LwIP/src/include/compat)
include_directories(Middlewares/Third_Party/LwIP/src/include/compat/posix)
include_directories(Middlewares/Third_Party/LwIP/src/include/compat/posix/arpa)
include_directories(Middlewares/Third_Party/LwIP/src/include/compat/posix/net)
include_directories(Middlewares/Third_Party/LwIP/src/include/compat/posix/sys)
include_directories(Middlewares/Third_Party/LwIP/src/include/compat/stdc)
include_directories(Middlewares/Third_Party/LwIP/src/include/lwip)
include_directories(Middlewares/Third_Party/LwIP/src/include/lwip/apps)
include_directories(Middlewares/Third_Party/LwIP/src/include/lwip/priv)
include_directories(Middlewares/Third_Party/LwIP/src/include/lwip/prot)
include_directories(Middlewares/Third_Party/LwIP/src/include/netif)
include_directories(Middlewares/Third_Party/LwIP/src/include/netif/ppp)
include_directories(Middlewares/Third_Party/LwIP/system)
include_directories(Middlewares/Third_Party/LwIP/system/arch)
include_directories(source/include)
include_directories(source/libexport/include)
include_directories(source/main_task/inc)
include_directories(source/mods/inc)
include_directories(source/platform/inc)

include_directories(
    /Inc
    /Drivers/STM32F4xx_HAL_Driver/Inc
    /Drivers/STM32F4xx_HAL_Driver/Inc/Legacy
    /Drivers/CMSIS/Device/ST/STM32F4xx/Include
    /Drivers/CMSIS/Include
    /source/include
    /source/libexport/include
    /Middlewares/Third_Party/LwIP/src/include
    /Middlewares/Third_Party/LwIP/system
    /Middlewares/Third_Party/LwIP/src/include/netif/ppp
    /Middlewares/Third_Party/LwIP/src/include/lwip
    /Middlewares/Third_Party/LwIP/src/include/lwip/apps
    /Middlewares/Third_Party/LwIP/src/include/lwip/priv
    /Middlewares/Third_Party/LwIP/src/include/lwip/prot
    /Middlewares/Third_Party/LwIP/src/include/netif
    /Middlewares/Third_Party/LwIP/src/include/compat/posix
    /Middlewares/Third_Party/LwIP/src/include/compat/posix/arpa
    /Middlewares/Third_Party/LwIP/src/include/compat/posix/net
    /Middlewares/Third_Party/LwIP/src/include/compat/posix/sys
    /Middlewares/Third_Party/LwIP/src/include/compat/stdc
    /Middlewares/Third_Party/LwIP/system/arch
    /source/main_task/inc
    /source/mods/inc
    /source/platform/inc
    /lib
)


add_executable(X13606_CUBEMX_GD32F470ZGT6
        Drivers/BSP/Components/dp83848/dp83848.c
        Drivers/BSP/Components/dp83848/dp83848.h
        Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f429xx.h
        Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f4xx.h
        Drivers/CMSIS/Device/ST/STM32F4xx/Include/system_stm32f4xx.h
        Drivers/CMSIS/Include/cachel1_armv7.h
        Drivers/CMSIS/Include/cmsis_armcc.h
        Drivers/CMSIS/Include/cmsis_armclang.h
        Drivers/CMSIS/Include/cmsis_armclang_ltm.h
        Drivers/CMSIS/Include/cmsis_compiler.h
        Drivers/CMSIS/Include/cmsis_gcc.h
        Drivers/CMSIS/Include/cmsis_iccarm.h
        Drivers/CMSIS/Include/cmsis_version.h
        Drivers/CMSIS/Include/core_armv81mml.h
        Drivers/CMSIS/Include/core_armv8mbl.h
        Drivers/CMSIS/Include/core_armv8mml.h
        Drivers/CMSIS/Include/core_cm0.h
        Drivers/CMSIS/Include/core_cm0plus.h
        Drivers/CMSIS/Include/core_cm1.h
        Drivers/CMSIS/Include/core_cm23.h
        Drivers/CMSIS/Include/core_cm3.h
        Drivers/CMSIS/Include/core_cm33.h
        Drivers/CMSIS/Include/core_cm35p.h
        Drivers/CMSIS/Include/core_cm4.h
        Drivers/CMSIS/Include/core_cm55.h
        Drivers/CMSIS/Include/core_cm7.h
        Drivers/CMSIS/Include/core_cm85.h
        Drivers/CMSIS/Include/core_sc000.h
        Drivers/CMSIS/Include/core_sc300.h
        Drivers/CMSIS/Include/core_starmc1.h
        Drivers/CMSIS/Include/mpu_armv7.h
        Drivers/CMSIS/Include/mpu_armv8.h
        Drivers/CMSIS/Include/pac_armv81.h
        Drivers/CMSIS/Include/pmu_armv8.h
        Drivers/CMSIS/Include/tz_context.h
        Drivers/STM32F4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_cortex.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_def.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_dma.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_dma_ex.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_eth.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_exti.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash_ex.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash_ramfunc.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_gpio.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_gpio_ex.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pwr.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pwr_ex.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rcc.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rcc_ex.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rtc.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rtc_ex.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_spi.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_tim.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_tim_ex.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_uart.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_bus.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_cortex.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_dma.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_exti.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_gpio.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_pwr.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_rcc.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_rtc.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_spi.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_system.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_tim.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_usart.h
        Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_utils.h
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_eth.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c
        Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c
        Inc/dma.h
        Inc/ethernetif.h
        Inc/gpio.h
        Inc/lwip.h
        Inc/lwipopts.h
        Inc/main.h
        Inc/rtc.h
        Inc/spi.h
        Inc/stm32f4xx_hal_conf.h
        Inc/stm32f4xx_it.h
        Inc/tim.h
        Inc/usart.h
        lib/arm_math.h
        MDK-ARM/RTE/_X13606_CUBEMX_GD32F470ZGT6/RTE_Components.h
        Middlewares/Third_Party/LwIP/src/api/api_lib.c
        Middlewares/Third_Party/LwIP/src/api/api_msg.c
        Middlewares/Third_Party/LwIP/src/api/err.c
        Middlewares/Third_Party/LwIP/src/api/if_api.c
        Middlewares/Third_Party/LwIP/src/api/netbuf.c
        Middlewares/Third_Party/LwIP/src/api/netdb.c
        Middlewares/Third_Party/LwIP/src/api/netifapi.c
        Middlewares/Third_Party/LwIP/src/api/sockets.c
        Middlewares/Third_Party/LwIP/src/api/tcpip.c
        Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c
        Middlewares/Third_Party/LwIP/src/core/ipv4/autoip.c
        Middlewares/Third_Party/LwIP/src/core/ipv4/dhcp.c
        Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c
        Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c
        Middlewares/Third_Party/LwIP/src/core/ipv4/igmp.c
        Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c
        Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_addr.c
        Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c
        Middlewares/Third_Party/LwIP/src/core/ipv6/dhcp6.c
        Middlewares/Third_Party/LwIP/src/core/ipv6/ethip6.c
        Middlewares/Third_Party/LwIP/src/core/ipv6/icmp6.c
        Middlewares/Third_Party/LwIP/src/core/ipv6/inet6.c
        Middlewares/Third_Party/LwIP/src/core/ipv6/ip6.c
        Middlewares/Third_Party/LwIP/src/core/ipv6/ip6_addr.c
        Middlewares/Third_Party/LwIP/src/core/ipv6/ip6_frag.c
        Middlewares/Third_Party/LwIP/src/core/ipv6/mld6.c
        Middlewares/Third_Party/LwIP/src/core/ipv6/nd6.c
        Middlewares/Third_Party/LwIP/src/core/altcp.c
        Middlewares/Third_Party/LwIP/src/core/altcp_alloc.c
        Middlewares/Third_Party/LwIP/src/core/altcp_tcp.c
        Middlewares/Third_Party/LwIP/src/core/def.c
        Middlewares/Third_Party/LwIP/src/core/dns.c
        Middlewares/Third_Party/LwIP/src/core/inet_chksum.c
        Middlewares/Third_Party/LwIP/src/core/init.c
        Middlewares/Third_Party/LwIP/src/core/ip.c
        Middlewares/Third_Party/LwIP/src/core/mem.c
        Middlewares/Third_Party/LwIP/src/core/memp.c
        Middlewares/Third_Party/LwIP/src/core/netif.c
        Middlewares/Third_Party/LwIP/src/core/pbuf.c
        Middlewares/Third_Party/LwIP/src/core/raw.c
        Middlewares/Third_Party/LwIP/src/core/stats.c
        Middlewares/Third_Party/LwIP/src/core/sys.c
        Middlewares/Third_Party/LwIP/src/core/tcp.c
        Middlewares/Third_Party/LwIP/src/core/tcp_in.c
        Middlewares/Third_Party/LwIP/src/core/tcp_out.c
        Middlewares/Third_Party/LwIP/src/core/timeouts.c
        Middlewares/Third_Party/LwIP/src/core/udp.c
        Middlewares/Third_Party/LwIP/src/include/compat/posix/arpa/inet.h
        Middlewares/Third_Party/LwIP/src/include/compat/posix/net/if.h
        Middlewares/Third_Party/LwIP/src/include/compat/posix/sys/socket.h
        Middlewares/Third_Party/LwIP/src/include/compat/posix/netdb.h
        Middlewares/Third_Party/LwIP/src/include/compat/stdc/errno.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/altcp_proxyconnect.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/altcp_tls_mbedtls_opts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/fs.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/http_client.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/httpd.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/httpd_opts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/lwiperf.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/mdns.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/mdns_opts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/mdns_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/mqtt.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/mqtt_opts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/mqtt_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/netbiosns.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/netbiosns_opts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/smtp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/smtp_opts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmp_core.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmp_mib2.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmp_opts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmp_scalar.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmp_snmpv2_framework.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmp_snmpv2_usm.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmp_table.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmp_threadsync.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/snmpv3.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/sntp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/sntp_opts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/tftp_opts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/apps/tftp_server.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/altcp_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/api_msg.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/mem_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/memp_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/memp_std.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/nd6_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/raw_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/sockets_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcp_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcpip_priv.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/autoip.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/dhcp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/dhcp6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/dns.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/etharp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/ethernet.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/iana.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/icmp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/icmp6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/ieee.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/igmp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/ip.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/ip4.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/ip6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/mld6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/nd6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/tcp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/prot/udp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/altcp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/altcp_tcp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/altcp_tls.h
        Middlewares/Third_Party/LwIP/src/include/lwip/api.h
        Middlewares/Third_Party/LwIP/src/include/lwip/arch.h
        Middlewares/Third_Party/LwIP/src/include/lwip/autoip.h
        Middlewares/Third_Party/LwIP/src/include/lwip/debug.h
        Middlewares/Third_Party/LwIP/src/include/lwip/def.h
        Middlewares/Third_Party/LwIP/src/include/lwip/dhcp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/dhcp6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/dns.h
        Middlewares/Third_Party/LwIP/src/include/lwip/err.h
        Middlewares/Third_Party/LwIP/src/include/lwip/errno.h
        Middlewares/Third_Party/LwIP/src/include/lwip/etharp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ethip6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/icmp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/icmp6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/if_api.h
        Middlewares/Third_Party/LwIP/src/include/lwip/igmp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/inet.h
        Middlewares/Third_Party/LwIP/src/include/lwip/inet_chksum.h
        Middlewares/Third_Party/LwIP/src/include/lwip/init.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ip.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ip4.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ip4_frag.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ip6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ip6_addr.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ip6_frag.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ip6_zone.h
        Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h
        Middlewares/Third_Party/LwIP/src/include/lwip/mem.h
        Middlewares/Third_Party/LwIP/src/include/lwip/memp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/mld6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/nd6.h
        Middlewares/Third_Party/LwIP/src/include/lwip/netbuf.h
        Middlewares/Third_Party/LwIP/src/include/lwip/netdb.h
        Middlewares/Third_Party/LwIP/src/include/lwip/netif.h
        Middlewares/Third_Party/LwIP/src/include/lwip/netifapi.h
        Middlewares/Third_Party/LwIP/src/include/lwip/opt.h
        Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h
        Middlewares/Third_Party/LwIP/src/include/lwip/raw.h
        Middlewares/Third_Party/LwIP/src/include/lwip/sio.h
        Middlewares/Third_Party/LwIP/src/include/lwip/snmp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/sockets.h
        Middlewares/Third_Party/LwIP/src/include/lwip/stats.h
        Middlewares/Third_Party/LwIP/src/include/lwip/sys.h
        Middlewares/Third_Party/LwIP/src/include/lwip/tcp.h
        Middlewares/Third_Party/LwIP/src/include/lwip/tcpbase.h
        Middlewares/Third_Party/LwIP/src/include/lwip/tcpip.h
        Middlewares/Third_Party/LwIP/src/include/lwip/timeouts.h
        Middlewares/Third_Party/LwIP/src/include/lwip/udp.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/ccp.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/chap-md5.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/chap-new.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/chap_ms.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/eap.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/ecp.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/eui64.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/fsm.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/ipcp.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/ipv6cp.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/lcp.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/magic.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/mppe.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/ppp.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/ppp_impl.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/ppp_opts.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/pppapi.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/pppcrypt.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/pppdebug.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/pppoe.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/pppol2tp.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/pppos.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/upap.h
        Middlewares/Third_Party/LwIP/src/include/netif/ppp/vj.h
        Middlewares/Third_Party/LwIP/src/include/netif/bridgeif.h
        Middlewares/Third_Party/LwIP/src/include/netif/bridgeif_opts.h
        Middlewares/Third_Party/LwIP/src/include/netif/etharp.h
        Middlewares/Third_Party/LwIP/src/include/netif/ethernet.h
        Middlewares/Third_Party/LwIP/src/include/netif/ieee802154.h
        Middlewares/Third_Party/LwIP/src/include/netif/lowpan6.h
        Middlewares/Third_Party/LwIP/src/include/netif/lowpan6_ble.h
        Middlewares/Third_Party/LwIP/src/include/netif/lowpan6_common.h
        Middlewares/Third_Party/LwIP/src/include/netif/lowpan6_opts.h
        Middlewares/Third_Party/LwIP/src/include/netif/slipif.h
        Middlewares/Third_Party/LwIP/src/include/netif/zepif.h
        Middlewares/Third_Party/LwIP/src/netif/ppp/auth.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/ccp.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/chap-md5.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/chap-new.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/chap_ms.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/demand.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/eap.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/ecp.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/eui64.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/fsm.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/ipcp.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/ipv6cp.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/lcp.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/magic.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/mppe.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/multilink.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/ppp.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/pppapi.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/pppcrypt.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/pppoe.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/pppol2tp.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/pppos.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/upap.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/utils.c
        Middlewares/Third_Party/LwIP/src/netif/ppp/vj.c
        Middlewares/Third_Party/LwIP/src/netif/bridgeif.c
        Middlewares/Third_Party/LwIP/src/netif/bridgeif_fdb.c
        Middlewares/Third_Party/LwIP/src/netif/ethernet.c
        Middlewares/Third_Party/LwIP/src/netif/lowpan6.c
        Middlewares/Third_Party/LwIP/src/netif/lowpan6_ble.c
        Middlewares/Third_Party/LwIP/src/netif/lowpan6_common.c
        Middlewares/Third_Party/LwIP/src/netif/slipif.c
        Middlewares/Third_Party/LwIP/src/netif/zepif.c
        Middlewares/Third_Party/LwIP/system/arch/bpstruct.h
        Middlewares/Third_Party/LwIP/system/arch/cc.h
        Middlewares/Third_Party/LwIP/system/arch/cpu.h
        Middlewares/Third_Party/LwIP/system/arch/epstruct.h
        Middlewares/Third_Party/LwIP/system/arch/init.h
        Middlewares/Third_Party/LwIP/system/arch/lib.h
        Middlewares/Third_Party/LwIP/system/arch/perf.h
        Middlewares/Third_Party/LwIP/system/arch/sys_arch.h
        source/include/xsj_lib.h
        source/libexport/fm25v02/fm25v02.c
        source/libexport/ad_scck/bsp_ad7616.c

        source/libexport/excitation/Excitation.c
        source/libexport/excitation/s7kmCommon.c
        source/libexport/excitation/s7kmReceive.c
        source/libexport/excitation/s7kmSend.c
        source/libexport/excitation/Serial7000Master.c

        source/libexport/gpio/gpio_xsj.c
        source/libexport/include/fm25v02.h
        source/libexport/include/bsp_ad7616.h
        source/libexport/include/gpio_xsj.h
        source/libexport/include/spi_xsj.h
        source/libexport/include/tcp_client.h
        source/libexport/include/tcp_server.h
        source/libexport/include/uart.h
        source/libexport/include/udp_client.h
        source/libexport/include/udp_server.h
        source/libexport/include/w25qxx.h

        source/libexport/include/excitation_def.h
        source/libexport/include/Excitation.h
        source/libexport/include/s7kmCommon.h
        source/libexport/include/s7kmReceive.h
        source/libexport/include/s7kmSend.h
        source/libexport/include/Serial7000Master.h

        source/libexport/spi/spi_xsj.c
        source/libexport/tcp_1/tcp_client.c
        source/libexport/tcp_1/tcp_server.c
        source/libexport/uart/uart.c
        source/libexport/udp_1/udp_client.c
        source/libexport/udp_1/udp_server.c
        source/libexport/w25q64/w25qxx.c
        source/libexport/xsj_lib.c
        source/main_task/inc/testByCJW.h
        source/main_task/inc/umain.h
        source/main_task/testByCJW.c
        source/main_task/umain.c

        source/mods/inc/appDevice.h
        source/mods/inc/fun.h
        source/mods/inc/exchangeData.h
        source/mods/inc/ctrl.h
        source/mods/inc/mod_bi.h
        source/mods/inc/mod_bo.h
        source/mods/inc/mod_CTFail.h
        source/mods/inc/mod_cur.h
        source/mods/inc/mod_general.h
        source/mods/inc/mod_getAngle.h
        source/mods/inc/mod_measData.h
        source/mods/inc/mod_negOverCur.h
        source/mods/inc/mod_nonElec.h
        source/mods/inc/mod_noVol.h
        source/mods/inc/mod_overCur.h
        source/mods/inc/mod_overFreq.h
        source/mods/inc/mod_overLoad.h
        source/mods/inc/mod_overSpeed.h
        source/mods/inc/mod_overVol.h
        source/mods/inc/mod_PTFail.h
        source/mods/inc/mod_rmtCtl.h
        source/mods/inc/mod_rvsPwr.h
        source/mods/inc/mod_selfChk.h
        source/mods/inc/mod_sglOverCur.h
        source/mods/inc/mod_synCtr.h
        source/mods/inc/mod_underExc.h
        source/mods/inc/mod_underFreq.h
        source/mods/inc/mod_vol.h
        source/mods/appDevice.c
        source/mods/fun.c
        source/mods/LANGUAGE.c
        source/mods/mod_bi.c
        source/mods/mod_bo.c
        source/mods/mod_CTFail.c
        source/mods/mod_cur.c
        source/mods/mod_general.c
        source/mods/mod_getAngle.c
        source/mods/mod_measData.c
        source/mods/mod_negOverCur.c
        source/mods/mod_nonElec.c
        source/mods/mod_noVol.c
        source/mods/mod_overCur.c
        source/mods/mod_overFreq.c
        source/mods/mod_overLoad.c
        source/mods/mod_overSpeed.c
        source/mods/mod_overVol.c
        source/mods/mod_PTFail.c
        source/mods/mod_rmtCtl.c
        source/mods/mod_rvsPwr.c
        source/mods/mod_selfChk.c
        source/mods/mod_sglOverCur.c
        source/mods/mod_synCtr.c
        source/mods/mod_underExc.c
        source/mods/mod_underFreq.c
        source/mods/mod_vol.c
        source/mods/PARA_GROUPS.c
        source/mods/REF_TABLES.c

        source/platform/bcode_task/bcode_task.c
        source/platform/com_task/N103App.c
        source/platform/com_task/N103Link.c
        source/platform/com_task/reftable103.c
        source/platform/com_task/S103App.c
        source/platform/com_task/S103Link.c
        source/platform/common/ana.c
        source/platform/common/bi.c
        source/platform/common/common.c
        source/platform/common/control.c
        source/platform/common/keepStatus.c
        source/platform/common/setting.c
        source/platform/common/task_queue.c
        source/platform/inc/ana.h
        source/platform/inc/bcode_task.h
        source/platform/inc/bi.h
        source/platform/inc/common.h
        source/platform/inc/config.h
        source/platform/inc/control.h
        source/platform/inc/DATATypeDEF.H
        source/platform/inc/eventRefTable.h
        source/platform/inc/IEC103Def.h
        source/platform/inc/keepStatus.h
        source/platform/inc/language.h
        source/platform/inc/lb_task.h
        source/platform/inc/N103Slave.h
        source/platform/inc/Modbus.h
        source/platform/inc/reftable103.h
        source/platform/inc/rtc_task.h
        source/platform/inc/S103Slave.h
        source/platform/inc/sample_task.h
        source/platform/inc/setting.h
        source/platform/inc/soe_task.h
        source/platform/inc/task_queue.h
        source/platform/hmi_task/Modbus.c
        source/platform/inc/plc_comm.h
        source/platform/inc/plc_reftable.h
        source/platform/plc_task/plc_comm.c
        source/platform/plc_task/plc_reftable.c
        source/platform/lb_task/lb_task.c
        source/platform/rtc_task/rtc_task.c
        source/platform/sample_task/sample_task.c
        source/platform/soe_task/eventRefTable.c
        source/platform/soe_task/opRefTable.c
        source/platform/soe_task/soe_task.c
        source/tmain/tadc.c
        source/tmain/tBcod.c
        source/tmain/teth.c
        source/tmain/tflash.c
        source/tmain/tfram.c
        source/tmain/tgpio.c
        source/tmain/trtc.c
        source/tmain/tspi.c
        source/tmain/tuart.c
        source/tmain/umain.c
        Src/dma.c
        Src/ethernetif.c
        Src/gpio.c
        Src/lwip.c
        Src/main.c
        Src/rtc.c
        Src/spi.c
        Src/stm32f4xx_hal_msp.c
        Src/stm32f4xx_it.c
        Src/system_stm32f4xx.c
        Src/tim.c
        Src/usart.c)
