#include "mod_overLoad.h"
/*过负荷保护*/

void runModOverLoad(s_modOverLoad* dp)
{

	//将三相电流依次比较
	overRelay(&dp->flg_qd,dp->i_Min,dp->set_i_overload,&(dp->timebuf_qd),0.95,1);

	//告警
	dp->op_overload_alarm = fDelayReturnRelay((dp->setSwit_overload) & dp->flg_qd,&(dp->timebuf_alarm),dp->set_t_overload,500);

	return;
}

int initModOverLoad(s_modOverLoad* dp, char* dpName)
{
	dp->dpName = dpName;
	//注册开关量
	regBI(dpName, "op_overload_alarm", 0, &dp->op_overload_alarm);
	
	//注册告警信息
	regChkElement(&dp->op_overload_alarm);
	
	//加入队列
	addTaskLevel2(runModOverLoad, dp);
	return SUCC;
}

int initModParmOverLoad(s_modOverLoad* dp)
{
	//获取定值
	
	//无软压板
	//dp->vbi_overload = getSetByDesc("vbi_overload");	//软压板
	dp->setSwit_overload = getSetByDesc( "setSwit_overload");	//控制字
	dp->set_i_overload = getSetByDesc( "set_i_overload");
	dp->set_t_overload = getSetByDesc("set_t_overload");  //
	dp->trip_matrix = getSetByDesc("trip_matrix_overload");

	return SUCC;
	
}