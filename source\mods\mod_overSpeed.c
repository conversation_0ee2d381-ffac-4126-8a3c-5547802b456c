#include "mod_overSpeed.h"
/*过速保护/转速保护*/

void runModOverSpeed(s_modOverSpeed* dp)
{
	int fm,n;
	fm = *(dp->fm) * DEFAULT_DIV_VAL;  // Assigning the value of dp->fm to fm
	n = fm/50;
	//将三相电流依次比较
	overRelay(&dp->flg_qd,&n,dp->set_i_overspeed,&(dp->timebuf_qd),0.95,1);
	overRelay(&dp->flg_alarm,&n,dp->set_i_overspeed_alarm,&(dp->timebuf_alarm),0.95,1);
	overRelay(&dp->qd_overspeed_qd,&n,dp->set_i_overspeed*0.95,&(dp->timebuf_qd2),1.0,1);
	dp->qd_overspeed_qd &= dp->setSwit_overspeed;
	
	//告警
	dp->op_overspeed_alarm = fDelayRelay((dp->setSwit_overspeed_alarm ^0x1) & dp->flg_alarm,&(dp->timebuf_alarm),dp->set_t_overspeed_alarm);

	//动作
	dp->qd_overspeed = dp->setSwit_overspeed & dp->flg_qd;
	dp->op_overspeed = fDelayRelay(dp->qd_overspeed ,&dp->timebuf_op,dp->set_t_overspeed);
	
	return;
}

int initModOverSpeed(s_modOverSpeed* dp, char* dpName)
{
	dp->dpName = dpName;
	//注册开关量
	regBI(dpName, "op_overspeed", 0, &dp->op_overspeed);
	regBI(dpName, "op_overspeed_alarm", 0, &dp->op_overspeed_alarm);
	
	//注册告警信息
	regChkElement(&dp->op_overspeed_alarm);
	
	//注册启动元件
	regQdElement(&dp->qd_overspeed_qd);
	
	//注册动作元件
	regTripElement(&dp->op_overspeed, &dp->trip_matrix);
	
	//加入队列
	addTaskLevel2(runModOverSpeed, dp);
	return SUCC;
}

int initModParmOverSpeed(s_modOverSpeed* dp)
{
	//获取定值
	
	//无软压板
	//dp->vbi_overspeed = getSetByDesc("vbi_overspeed");	//软压板
	dp->setSwit_overspeed = getSetByDesc( "setSwit_overspeed");	//控制字
	dp->setSwit_overspeed_alarm = getSetByDesc( "setSwit_overspeed_alarm");	//控制字
	dp->set_i_overspeed = getSetByDesc( "set_i_overspeed");
	dp->set_t_overspeed = getSetByDesc("set_t_overspeed");  //
	dp->set_i_overspeed_alarm = getSetByDesc( "set_i_overspeed_alarm");
	dp->set_t_overspeed_alarm = getSetByDesc("set_t_overspeed_alarm");  //
	dp->trip_matrix = getSetByDesc("trip_matrix_overspeed");

	return SUCC;
	
}