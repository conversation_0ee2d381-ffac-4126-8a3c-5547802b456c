/**
 * @file ad7792.h
 * <AUTHOR> pinghui
 * @brief ad7792驱动程序
 * @version 0.1
 * @date 2025-06-18
 * 
 * @copyright Copyright (c) 2025
 * 
 */

#ifndef __AD7792_H__
#define __AD7792_H__

#include "main.h"
#include "stdint.h"
#include "spi.h"
#include "config.h"
#include "Temp_ReferTable.h"
#include "stm32f429xx.h"
#include "stm32f4xx_hal.h"
#include "stm32f4xx_hal_gpio.h"
#include "stm32f4xx_hal_spi.h"
#include "stm32f4xx_hal_exti.h"
#include "stm32f4xx_hal_gpio_ex.h"
#include "DATATypeDEF.H"

// 更清晰的寄存器定义
#define AD7792_REG_COMM    0x00
#define AD7792_REG_STATUS  0x00
#define AD7792_REG_MODE    0x01
#define AD7792_REG_CONFIG  0x02
#define AD7792_REG_DATA    0x03
#define AD7792_REG_ID      0x04
#define AD7792_REG_IO      0x05
#define AD7792_REG_OFFSET  0x06
#define AD7792_REG_FS      0x07

#define  AveNum 10
#define  AD7792_CHANNEL_NUM		14
#define	TEMP_0_STARTCHANNEL		0
#define	TEMP_0_ENDCHANNEL		7

#define debug_AD7792  1

// 电子开关控制口线
// M_WDMUX1 -- PG3
#define		M_WDMUX1_GPIO_PORT		GPIOG
#define		M_WDMUX1_GPIO_PIN		GPIO_PIN_3
#define		M_WDMUX1_CLR	        HAL_GPIO_WritePin(M_WDMUX1_GPIO_PORT, M_WDMUX1_GPIO_PIN, GPIO_PIN_RESET)
#define		M_WDMUX1_SET	        HAL_GPIO_WritePin(M_WDMUX1_GPIO_PORT, M_WDMUX1_GPIO_PIN, GPIO_PIN_SET)

// M_WDMUX2 -- PG2
#define		M_WDMUX2_GPIO_PORT		GPIOG
#define		M_WDMUX2_GPIO_PIN		GPIO_PIN_2
#define		M_WDMUX2_CLR	        HAL_GPIO_WritePin(M_WDMUX2_GPIO_PORT, M_WDMUX2_GPIO_PIN, GPIO_PIN_RESET)
#define		M_WDMUX2_SET	        HAL_GPIO_WritePin(M_WDMUX2_GPIO_PORT, M_WDMUX2_GPIO_PIN, GPIO_PIN_SET)

// M_WDMUX3 -- PD15
#define		M_WDMUX3_GPIO_PORT		GPIOD
#define		M_WDMUX3_GPIO_PIN		GPIO_PIN_15
#define		M_WDMUX3_CLR	        HAL_GPIO_WritePin(M_WDMUX3_GPIO_PORT, M_WDMUX3_GPIO_PIN, GPIO_PIN_RESET)
#define		M_WDMUX3_SET	        HAL_GPIO_WritePin(M_WDMUX3_GPIO_PORT, M_WDMUX3_GPIO_PIN, GPIO_PIN_SET)

// M_WDMUX4 -- PD14
#define		M_WDMUX4_GPIO_PORT		GPIOD
#define		M_WDMUX4_GPIO_PIN		GPIO_PIN_14
#define		M_WDMUX4_CLR	        HAL_GPIO_WritePin(M_WDMUX4_GPIO_PORT, M_WDMUX4_GPIO_PIN, GPIO_PIN_RESET)
#define		M_WDMUX4_SET	        HAL_GPIO_WritePin(M_WDMUX4_GPIO_PORT, M_WDMUX4_GPIO_PIN, GPIO_PIN_SET)

// M_WDMUX5 -- PD13
#define		M_WDMUX5_GPIO_PORT		GPIOD
#define		M_WDMUX5_GPIO_PIN		GPIO_PIN_13
#define		M_WDMUX5_CLR	        HAL_GPIO_WritePin(M_WDMUX5_GPIO_PORT, M_WDMUX5_GPIO_PIN, GPIO_PIN_RESET)
#define		M_WDMUX5_SET	        HAL_GPIO_WritePin(M_WDMUX5_GPIO_PORT, M_WDMUX5_GPIO_PIN, GPIO_PIN_SET)

// M_WDMUX6 -- PD12
#define		M_WDMUX6_GPIO_PORT		GPIOD
#define		M_WDMUX6_GPIO_PIN		GPIO_PIN_12
#define		M_WDMUX6_CLR	        HAL_GPIO_WritePin(M_WDMUX6_GPIO_PORT, M_WDMUX6_GPIO_PIN, GPIO_PIN_RESET)
#define		M_WDMUX6_SET	        HAL_GPIO_WritePin(M_WDMUX6_GPIO_PORT, M_WDMUX6_GPIO_PIN, GPIO_PIN_SET)

typedef struct _AVE_DC                          				
{                                       					
	int32 DCvalue;					//直流当前值
	int32 DCvalueBuf[AveNum];		//直流值缓存
	int32 AVEdata;					//直流平均值
	uint16 LastPos;					//缓冲区位置
	int32 TempValue;				//温度值
	float TempResist;				//温度电阻值
}AVE_DC; 

extern void AD7792_Init(void);
extern void AD7792_Switch(uint8 channel);
extern void readAD7792Data(void);
extern void ADDelay(uint32 ADD_Time);
extern void BubbleSort(uint32 length, int32 *array);
extern void GPIO_MUX_Init(void);
extern void CalculateAve(AVE_DC *Temp);
extern uint8_t VerifyAD7792Communication(void);
extern uint8_t VerifyAD7792RegisiterRW(uint8_t regAddr, uint8_t* writeData, uint8_t bytesNum);
extern uint8_t ReadAndVerifyRegister(uint8_t regAddr, uint8_t* expectedData, uint8_t bytesNum);
extern void Cal_ThermistorTemp(uint8 DC_Channel);
extern int32 Temp_Calibrate(uint8 DC_Channel, uint8 calibType);
extern void calculateK1K2(uint8_t channelIndex, uint8_t sensorType, float *measuredR1, float actualR1,float *measuredR2, float actualR2);

#endif
