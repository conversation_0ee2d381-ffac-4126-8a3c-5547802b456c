#ifndef _MOD_OVERLOAD_H_
#define _MOD_OVERLOAD_H_
#include "task_queue.h"
#include "fun.h"

typedef struct
{
	char* dpName;
	
	//输入信号
	int* ia;
	int* i_Min;
	
	//输出信号
	unsigned char op_overload_alarm;
	
	//输出模拟量
	
	//定值
	//unsigned char vbi_overload;		//软压板
	unsigned char setSwit_overload; //控制字
	int set_i_overload;	//过负荷定值
	unsigned int set_t_overload;  //过负荷时间
	int trip_matrix;	//
	
	//内部用
	int timebuf_qd;
	int timebuf_alarm;
	unsigned char flg_qd;
	
}s_modOverLoad;

int initModOverLoad(s_modOverLoad* dp, char* dpName);
int initModParmOverLoad(s_modOverLoad* dp);

#endif