/**
  *****************************************************************************
  * @file    tcp_plc_server.c
  * <AUTHOR> @version 
  * @date    
  * @brief   PLC专用TCP服务端的实现
  *****************************************************************************
  */

#include "tcp_plc_server.h"
#include "string.h"

/* 定义PLC TCP服务器的端口号 */
#define TCP_PLC_LOCAL_PORT     2404  /* PLC TCP服务器本地端口 */
#define TCP_PLC_REMOTE_PORT    12    /* PLC TCP服务器远端端口 */

extern ip4_addr_t ipaddr;

static TCP_PLC_RECBUF_ST tcp_plc_rec_buf_st;                      //PLC TCP服务器接收缓冲区
static P_TCP_PLC_RECBUF_ST p_tcp_plc_rec_buf_st = &tcp_plc_rec_buf_st;

static char  g_plc_client_stat = 0;                               //PLC TCP服务器客户端状态
static struct tcp_pcb *g_plc_tpcb;                                //PLC TCP服务器PCB

void combuf_data_font(unsigned char *buf, uint32_t blen, uint32_t num);

/******************************************************************************
 * 描述  : PLC TCP服务器接收回调函数
 * 参数  : -
 * 返回  : -
******************************************************************************/
static err_t tcp_plc_server_recv(void *arg, struct tcp_pcb *tpcb,
                                 struct pbuf *p, err_t err)
{
    if (p != NULL)
    {
        struct pbuf *ptmp = p;
        uint32_t xlen;
        
        while(ptmp != NULL)
        {                
            xlen =  p_tcp_plc_rec_buf_st->rev_len +  p->len;  
		    if (xlen > TCP_PLC_REC_BUF_LEN)
		    {
                if( p->len <= TCP_PLC_REC_BUF_LEN)
                {                                                                   
		    	     combuf_data_font( p_tcp_plc_rec_buf_st->rev_buf, TCP_PLC_REC_BUF_LEN, xlen - TCP_PLC_REC_BUF_LEN);
		    	     p_tcp_plc_rec_buf_st->rev_len -= (xlen - TCP_PLC_REC_BUF_LEN);
		    	     memcpy( p_tcp_plc_rec_buf_st->rev_buf +  p_tcp_plc_rec_buf_st->rev_len, (char *)p->payload,  p->len);
                     p_tcp_plc_rec_buf_st->rev_len +=  p->len;                    
                }
                else   //收到的数据，大于缓冲区长度，取最后的数据
                {
                    memcpy( p_tcp_plc_rec_buf_st->rev_buf , ((char *)p->payload + p->len - TCP_PLC_REC_BUF_LEN),  TCP_PLC_REC_BUF_LEN);
                    p_tcp_plc_rec_buf_st->rev_len =  TCP_PLC_REC_BUF_LEN;                                                
                }                                
                
		    }
		    else
		    {
		    	 memcpy( p_tcp_plc_rec_buf_st->rev_buf +  p_tcp_plc_rec_buf_st->rev_len, (char *)p->payload,  p->len);
                 p_tcp_plc_rec_buf_st->rev_len +=  p->len;
		    }		    
                        
            ptmp = p->next;
        }          
        
        /* 更新接收窗口 */
        tcp_recved(tpcb, p->tot_len);
        
        /* 释放缓冲区数据 */
        pbuf_free(p);
    }
    else if (err == ERR_OK)
    {
        //printf("plc tcp client closed\r\n");
        
        g_plc_client_stat = 0;
        tcp_recved(tpcb, p->tot_len);
        
        return tcp_close(tpcb);
    }

    return ERR_OK;
}

/******************************************************************************
 * 描述  : PLC TCP服务器连接接受回调函数
 * 参数  : -
 * 返回  : -
******************************************************************************/
static err_t tcp_plc_server_accept(void *arg, struct tcp_pcb *newpcb, err_t err)
{
    LWIP_UNUSED_ARG(arg);
    LWIP_UNUSED_ARG(err);

    /* 设置接收回调函数 */
    tcp_setprio(newpcb, TCP_PRIO_MIN);
    
    /* 设置客户端状态为连接 */
    g_plc_client_stat = 1;
    g_plc_tpcb = newpcb;
    
    /* 注册接收回调函数 */
    tcp_recv(newpcb, tcp_plc_server_recv);

    return ERR_OK;
}

/******************************************************************************
 * 描述  : 创建PLC TCP服务器
 * 参数  : 无
 * 返回  : 无
******************************************************************************/
void tcp_plc_server_init(void)
{
    struct tcp_pcb *tpcb;

    memset(p_tcp_plc_rec_buf_st, 0, sizeof(tcp_plc_rec_buf_st)); //PLC TCP服务器内部数据接收区
    
    /* 创建tcp控制块 */
    tpcb = tcp_new();   
    g_plc_tpcb = tpcb;
    
    if (tpcb != NULL)
    {
        err_t err;
        
        /* 绑定端口接收，接收对象为所有ip地址 */
        err = tcp_bind(tpcb, IP_ADDR_ANY, TCP_PLC_LOCAL_PORT);

        if (err == ERR_OK)
        {
            /* 监听 */
            tpcb = tcp_listen(tpcb);

            /* 注册接入回调函数 */
            tcp_accept(tpcb, tcp_plc_server_accept);
            
            g_plc_tpcb = tpcb;
            
            printf("PLC tcp server listening\r\n");
            printf("PLC tcp server ip:%d:%d:%d:%d port:%d\r\n",
                *((uint8_t *)&ipaddr.addr),
                *((uint8_t *)&ipaddr.addr + 1),
                *((uint8_t *)&ipaddr.addr + 2),
                *((uint8_t *)&ipaddr.addr + 3),
                tpcb->local_port);
        }
        else
        {
            memp_free(MEMP_TCP_PCB, tpcb);
            
            printf("can not bind pcb for PLC tcp server\r\n");
        }
    }
}

//获取PLC TCP服务器传来的数据
int get_tcp_plc_server_data(char *buf, int len)
{
    int rlen = 0;
        
    if ( p_tcp_plc_rec_buf_st->rev_len > 0)
	{
		if (p_tcp_plc_rec_buf_st->rev_len > len)
		{
			memcpy(buf, p_tcp_plc_rec_buf_st->rev_buf, len);
			combuf_data_font(p_tcp_plc_rec_buf_st->rev_buf, TCP_PLC_REC_BUF_LEN, len);
			p_tcp_plc_rec_buf_st->rev_len -= len;
			rlen = len;
		}
		else
		{
			memcpy(buf, p_tcp_plc_rec_buf_st->rev_buf, p_tcp_plc_rec_buf_st->rev_len);
			combuf_data_font( p_tcp_plc_rec_buf_st->rev_buf, p_tcp_plc_rec_buf_st->rev_len, p_tcp_plc_rec_buf_st->rev_len);
			rlen = p_tcp_plc_rec_buf_st->rev_len;
			p_tcp_plc_rec_buf_st->rev_len = 0;			
		}
	}	
	
	return rlen;    
}

//PLC TCP服务器发送数据
int send_tcp_plc_server_data(char *buff_tx, int buff_tx_len)
{
	 err_t err;
    if( g_plc_client_stat)
	{
		err = tcp_write(g_plc_tpcb, buff_tx,  buff_tx_len, 1);
		tcp_output(g_plc_tpcb);
		if(err == ERR_OK)
        return 0;
		else
				return -1;
	}
    else
        return -1;
}

void send_tcp_plc_server_data_plc(uint8_t *buff_tx, uint16_t buff_tx_len)
{
    send_tcp_plc_server_data((char *)buff_tx, (int)buff_tx_len);
}

//获取PLC TCP服务器连接状态
int get_tcp_plc_server_status(void)
{
    return g_plc_client_stat;
}

/******************************** END OF FILE ********************************/
