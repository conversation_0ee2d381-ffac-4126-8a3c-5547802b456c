
#include "spi.h"
#include "spi_xsj.h"

uint16_t stm_spi2_recv(uint8_t* rec_buf,uint16_t buflen)
{
    if(!buflen )
        return 1; 
    
    return HAL_SPI_Receive(&hspi2, rec_buf, buflen, 0xFFFFFF); 
}


uint8_t stm_spi2_send(uint8_t* send_buf,uint16_t buflen)
{    
    if(!buflen || buflen >128 )
        return 1; 
        
    return HAL_SPI_Transmit(&hspi2, send_buf, buflen, 0xFFFFFF);  //1char  -->0xfff
}
