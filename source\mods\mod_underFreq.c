#include "mod_underFreq.h"
/*低频保护*/

void runModUnderFreq(s_modUnderFreq* dp)
{
	unsigned char flg_con, flg_tmp;
	int fm;
	fm = *(dp->fm) * DEFAULT_DIV_VAL;  // Assigning the value of dp->fm to fm
	// //闭锁条件
	if(fm>dp->set_f_uf)
	{
		dp->flg_blk = 1;
	}
	//投退条件
	flg_con = dp->vbi_uf & dp->setSwit_uf;
	
	flg_tmp = flg_con & (*dp->flg_abc);
	//频率小于低频定值	
	dp->qd_uf = ((*(dp->fm)*DEFAULT_DIV_VAL)<dp->set_f_uf_qd) & flg_tmp;
	//动作
	underRelay(&(dp->flg_uf_op_val), &fm, dp->set_f_uf, &(dp->timebuf_for_op), 0.95, 3);
	dp->op_uf = fDelayReturnRelay(dp->flg_uf_op_val & dp->qd_uf & dp->flg_blk,&(dp->timebuf_op),dp->set_t_uf,200);
	
	if((dp->op_uf == 0) && dp->op_uf_pre == 1)
	{
		//闭锁
		dp->flg_blk = 0;
	}
	
	dp->op_uf_pre = dp->op_uf;
	return;
}

int initModUnderFreq(s_modUnderFreq* dp, char* dpName)
{
	dp->dpName = dpName;
	
	//注册开关量
	regBI(dpName, "op_uf", 0, &dp->op_uf);	//动作信号
	regBI(dpName, "ovbi_uf", 0, &dp->ovbi_uf);	
	//注册模拟量
	
	//注册启动元件
	regQdElement(&dp->qd_uf);
	
	//注册动作元件
	regTripElement(&dp->op_uf, &dp->trip_matrix);
	
	//加入队列
	addTaskLevel2(runModUnderFreq, dp);
	
	return SUCC;
	
}
int initModParmUnderFreq(s_modUnderFreq* dp)
{
	//获取定值
	
	dp->vbi_uf = getSetByDesc("vbi_uf");	//软压板
	dp->ovbi_uf = dp->vbi_uf;
	dp->setSwit_uf = getSetByDesc("setSwit_uf");	//控制字
	dp->set_f_uf = getSetByDesc( "set_f_uf");	//低频定值
	dp->set_f_uf_qd = dp->set_f_uf+500;	//+0.05是门槛
	dp->set_t_uf = getSetByDesc( "set_t_uf"); //低频时间
	dp->trip_matrix = getSetByDesc("trip_matrix_uf");
	return SUCC;
	
}