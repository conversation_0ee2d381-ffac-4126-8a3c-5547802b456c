<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>X13606_CUBEMX_GD32F470ZGT6</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCompiler_506_Windows_x86_b960</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>GD32F470ZG</Device>
          <Vendor>GigaDevice</Vendor>
          <PackID>GigaDevice.GD32F4xx_DFP.3.2.0</PackID>
          <PackURL>https://gd32mcu.com/data/documents/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x070000) IRAM2(0x10000000,0x010000) IROM(0x08000000,0x0100000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0GD32F4xx_1MB -********** -********* -FP0($$Device:GD32F470ZG$Flash\GD32F4xx_1MB.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:GD32F470ZG$Device\F4XX\Include\gd32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:GD32F470ZG$SVD\GD32F4xx.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>X13606_CUBEMX_GD32F470ZGT6\</OutputDirectory>
          <OutputName>X13606_CUBEMX_GD32F470ZGT6</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bin -o "$<EMAIL>" "$<EMAIL>"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>0</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>1</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x70000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8040000</StartAddress>
                <Size>0x60000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x50000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>5</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_HAL_DRIVER,STM32F429xx,LWIP_NOASSERT,__FPU_PRESENT = 1U,ARM_MATH_CM4,__CC_ARM,__TARGET_FPU_VFP</Define>
              <Undefine></Undefine>
              <IncludePath>../Inc;../Drivers/STM32F4xx_HAL_Driver/Inc;../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy;../Drivers/BSP/Components/dp83848;../Drivers/CMSIS/Device/ST/STM32F4xx/Include;../Drivers/CMSIS/Include;../source/include;../source/libexport/include;../Middlewares/Third_Party/LwIP/src/include;../Middlewares/Third_Party/LwIP/system;../Middlewares/Third_Party/LwIP/src/include/netif/ppp;../Middlewares/Third_Party/LwIP/src/include/lwip;../Middlewares/Third_Party/LwIP/src/include/lwip/apps;../Middlewares/Third_Party/LwIP/src/include/lwip/priv;../Middlewares/Third_Party/LwIP/src/include/lwip/prot;../Middlewares/Third_Party/LwIP/src/include/netif;../Middlewares/Third_Party/LwIP/src/include/compat/posix;../Middlewares/Third_Party/LwIP/src/include/compat/posix/arpa;../Middlewares/Third_Party/LwIP/src/include/compat/posix/net;../Middlewares/Third_Party/LwIP/src/include/compat/posix/sys;../Middlewares/Third_Party/LwIP/src/include/compat/stdc;../Middlewares/Third_Party/LwIP/system/arch;../source/main_task/inc;../source/mods/inc;../source/platform/inc;../lib</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08040000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>X13606_CUBEMX_GD32F470ZGT6\X13606_CUBEMX_GD32F470ZGT6.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application/MDK-ARM</GroupName>
          <Files>
            <File>
              <FileName>startup_stm32f429xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>startup_stm32f429xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Application/User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/main.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/gpio.c</FilePath>
            </File>
            <File>
              <FileName>dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/dma.c</FilePath>
            </File>
            <File>
              <FileName>lwip.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/lwip.c</FilePath>
            </File>
            <File>
              <FileName>ethernetif.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/ethernetif.c</FilePath>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/rtc.c</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/spi.c</FilePath>
            </File>
            <File>
              <FileName>tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/tim.c</FilePath>
            </File>
            <File>
              <FileName>usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/stm32f4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/stm32f4xx_hal_msp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/BSP/Components</GroupName>
          <Files>
            <File>
              <FileName>dp83848.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/BSP/Components/dp83848/dp83848.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/STM32F4xx_HAL_Driver</GroupName>
          <Files>
            <File>
              <FileName>stm32f4xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash_ramfunc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_eth.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_eth.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rtc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/system_stm32f4xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Middlewares/LwIP</GroupName>
          <Files>
            <File>
              <FileName>auth.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/auth.c</FilePath>
            </File>
            <File>
              <FileName>ccp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/ccp.c</FilePath>
            </File>
            <File>
              <FileName>chap_ms.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/chap_ms.c</FilePath>
            </File>
            <File>
              <FileName>chap-md5.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/chap-md5.c</FilePath>
            </File>
            <File>
              <FileName>chap-new.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/chap-new.c</FilePath>
            </File>
            <File>
              <FileName>demand.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/demand.c</FilePath>
            </File>
            <File>
              <FileName>eap.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/eap.c</FilePath>
            </File>
            <File>
              <FileName>eui64.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/eui64.c</FilePath>
            </File>
            <File>
              <FileName>fsm.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/fsm.c</FilePath>
            </File>
            <File>
              <FileName>ipcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/ipcp.c</FilePath>
            </File>
            <File>
              <FileName>ipv6cp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/ipv6cp.c</FilePath>
            </File>
            <File>
              <FileName>lcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/lcp.c</FilePath>
            </File>
            <File>
              <FileName>magic.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/magic.c</FilePath>
            </File>
            <File>
              <FileName>mppe.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/mppe.c</FilePath>
            </File>
            <File>
              <FileName>multilink.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/multilink.c</FilePath>
            </File>
            <File>
              <FileName>ppp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/ppp.c</FilePath>
            </File>
            <File>
              <FileName>pppapi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/pppapi.c</FilePath>
            </File>
            <File>
              <FileName>pppcrypt.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/pppcrypt.c</FilePath>
            </File>
            <File>
              <FileName>pppoe.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/pppoe.c</FilePath>
            </File>
            <File>
              <FileName>pppol2tp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/pppol2tp.c</FilePath>
            </File>
            <File>
              <FileName>pppos.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/pppos.c</FilePath>
            </File>
            <File>
              <FileName>upap.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/upap.c</FilePath>
            </File>
            <File>
              <FileName>utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/utils.c</FilePath>
            </File>
            <File>
              <FileName>vj.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/vj.c</FilePath>
            </File>
            <File>
              <FileName>bridgeif.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/bridgeif.c</FilePath>
            </File>
            <File>
              <FileName>bridgeif_fdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/bridgeif_fdb.c</FilePath>
            </File>
            <File>
              <FileName>ethernet.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ethernet.c</FilePath>
            </File>
            <File>
              <FileName>lowpan6.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/lowpan6.c</FilePath>
            </File>
            <File>
              <FileName>lowpan6_ble.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/lowpan6_ble.c</FilePath>
            </File>
            <File>
              <FileName>lowpan6_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/lowpan6_common.c</FilePath>
            </File>
            <File>
              <FileName>slipif.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/slipif.c</FilePath>
            </File>
            <File>
              <FileName>zepif.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/zepif.c</FilePath>
            </File>
            <File>
              <FileName>ecp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/netif/ppp/ecp.c</FilePath>
            </File>
            <File>
              <FileName>api_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/api/api_lib.c</FilePath>
            </File>
            <File>
              <FileName>api_msg.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/api/api_msg.c</FilePath>
            </File>
            <File>
              <FileName>err.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/api/err.c</FilePath>
            </File>
            <File>
              <FileName>if_api.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/api/if_api.c</FilePath>
            </File>
            <File>
              <FileName>netbuf.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/api/netbuf.c</FilePath>
            </File>
            <File>
              <FileName>netdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/api/netdb.c</FilePath>
            </File>
            <File>
              <FileName>netifapi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/api/netifapi.c</FilePath>
            </File>
            <File>
              <FileName>sockets.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/api/sockets.c</FilePath>
            </File>
            <File>
              <FileName>tcpip.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/api/tcpip.c</FilePath>
            </File>
            <File>
              <FileName>altcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/altcp.c</FilePath>
            </File>
            <File>
              <FileName>altcp_alloc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/altcp_alloc.c</FilePath>
            </File>
            <File>
              <FileName>altcp_tcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/altcp_tcp.c</FilePath>
            </File>
            <File>
              <FileName>def.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/def.c</FilePath>
            </File>
            <File>
              <FileName>dns.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/dns.c</FilePath>
            </File>
            <File>
              <FileName>inet_chksum.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/inet_chksum.c</FilePath>
            </File>
            <File>
              <FileName>init.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/init.c</FilePath>
            </File>
            <File>
              <FileName>ip.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ip.c</FilePath>
            </File>
            <File>
              <FileName>mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/mem.c</FilePath>
            </File>
            <File>
              <FileName>memp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/memp.c</FilePath>
            </File>
            <File>
              <FileName>netif.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/netif.c</FilePath>
            </File>
            <File>
              <FileName>pbuf.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/pbuf.c</FilePath>
            </File>
            <File>
              <FileName>raw.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/raw.c</FilePath>
            </File>
            <File>
              <FileName>stats.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/stats.c</FilePath>
            </File>
            <File>
              <FileName>sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/sys.c</FilePath>
            </File>
            <File>
              <FileName>tcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/tcp.c</FilePath>
            </File>
            <File>
              <FileName>tcp_in.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/tcp_in.c</FilePath>
            </File>
            <File>
              <FileName>tcp_out.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/tcp_out.c</FilePath>
            </File>
            <File>
              <FileName>timeouts.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/timeouts.c</FilePath>
            </File>
            <File>
              <FileName>udp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/udp.c</FilePath>
            </File>
            <File>
              <FileName>autoip.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv4/autoip.c</FilePath>
            </File>
            <File>
              <FileName>dhcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv4/dhcp.c</FilePath>
            </File>
            <File>
              <FileName>etharp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c</FilePath>
            </File>
            <File>
              <FileName>icmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c</FilePath>
            </File>
            <File>
              <FileName>igmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv4/igmp.c</FilePath>
            </File>
            <File>
              <FileName>ip4.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c</FilePath>
            </File>
            <File>
              <FileName>ip4_addr.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_addr.c</FilePath>
            </File>
            <File>
              <FileName>ip4_frag.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c</FilePath>
            </File>
            <File>
              <FileName>dhcp6.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv6/dhcp6.c</FilePath>
            </File>
            <File>
              <FileName>ethip6.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv6/ethip6.c</FilePath>
            </File>
            <File>
              <FileName>icmp6.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv6/icmp6.c</FilePath>
            </File>
            <File>
              <FileName>inet6.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv6/inet6.c</FilePath>
            </File>
            <File>
              <FileName>ip6.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv6/ip6.c</FilePath>
            </File>
            <File>
              <FileName>ip6_addr.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv6/ip6_addr.c</FilePath>
            </File>
            <File>
              <FileName>ip6_frag.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv6/ip6_frag.c</FilePath>
            </File>
            <File>
              <FileName>mld6.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv6/mld6.c</FilePath>
            </File>
            <File>
              <FileName>nd6.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/core/ipv6/nd6.c</FilePath>
            </File>
            <File>
              <FileName>mqtt.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>libexport</GroupName>
          <Files>
            <File>
              <FileName>xsj_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\xsj_lib.c</FilePath>
            </File>
            <File>
              <FileName>gpio_xsj.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\gpio\gpio_xsj.c</FilePath>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\uart\uart.c</FilePath>
            </File>
            <File>
              <FileName>spi_xsj.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\spi\spi_xsj.c</FilePath>
            </File>
            <File>
              <FileName>fm25v02.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\fm25v02\fm25v02.c</FilePath>
            </File>
            <File>
              <FileName>w25qxx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\w25q64\w25qxx.c</FilePath>
            </File>
            <File>
              <FileName>tcp_client.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\tcp_1\tcp_client.c</FilePath>
            </File>
            <File>
              <FileName>tcp_server.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\tcp_1\tcp_server.c</FilePath>
            </File>
            <File>
              <FileName>udp_client.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\udp_1\udp_client.c</FilePath>
            </File>
            <File>
              <FileName>udp_server.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\udp_1\udp_server.c</FilePath>
            </File>
            <File>
              <FileName>Serial7000Master.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\excitation\Serial7000Master.c</FilePath>
            </File>
            <File>
              <FileName>Excitation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\excitation\Excitation.c</FilePath>
            </File>
            <File>
              <FileName>s7kmCommon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\excitation\s7kmCommon.c</FilePath>
            </File>
            <File>
              <FileName>s7kmSend.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\excitation\s7kmSend.c</FilePath>
            </File>
            <File>
              <FileName>s7kmReceive.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\excitation\s7kmReceive.c</FilePath>
            </File>
            <File>
              <FileName>bsp_ad7616.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\ad_scck\bsp_ad7616.c</FilePath>
            </File>
            <File>
              <FileName>upgrade_flag.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\upgrade_flag\upgrade_flag.c</FilePath>
            </File>
            <File>
              <FileName>tcp_plc_server.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\tcp_plc\tcp_plc_server.c</FilePath>
            </File>
            <File>
              <FileName>ad7792.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\ad_7792\ad7792.c</FilePath>
            </File>
            <File>
              <FileName>Temp_Referable.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\libexport\ad_7792\Temp_Referable.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>platform</GroupName>
          <Files>
            <File>
              <FileName>bcode_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\bcode_task\bcode_task.c</FilePath>
            </File>
            <File>
              <FileName>N103App.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\com_task\N103App.c</FilePath>
            </File>
            <File>
              <FileName>N103Link.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\com_task\N103Link.c</FilePath>
            </File>
            <File>
              <FileName>reftable103.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\com_task\reftable103.c</FilePath>
            </File>
            <File>
              <FileName>S103App.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\com_task\S103App.c</FilePath>
            </File>
            <File>
              <FileName>S103Link.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\com_task\S103Link.c</FilePath>
            </File>
            <File>
              <FileName>ana.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\common\ana.c</FilePath>
            </File>
            <File>
              <FileName>bi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\common\bi.c</FilePath>
            </File>
            <File>
              <FileName>common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\common\common.c</FilePath>
            </File>
            <File>
              <FileName>control.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\common\control.c</FilePath>
            </File>
            <File>
              <FileName>keepStatus.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\common\keepStatus.c</FilePath>
            </File>
            <File>
              <FileName>setting.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\common\setting.c</FilePath>
            </File>
            <File>
              <FileName>task_queue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\common\task_queue.c</FilePath>
            </File>
            <File>
              <FileName>lb_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\lb_task\lb_task.c</FilePath>
            </File>
            <File>
              <FileName>eventRefTable.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\soe_task\eventRefTable.c</FilePath>
            </File>
            <File>
              <FileName>opRefTable.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\soe_task\opRefTable.c</FilePath>
            </File>
            <File>
              <FileName>soe_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\soe_task\soe_task.c</FilePath>
            </File>
            <File>
              <FileName>rtc_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\rtc_task\rtc_task.c</FilePath>
            </File>
            <File>
              <FileName>sample_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\sample_task\sample_task.c</FilePath>
            </File>
            <File>
              <FileName>Modbus.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\hmi_task\Modbus.c</FilePath>
            </File>
            <File>
              <FileName>plc_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\plc_task\plc_comm.c</FilePath>
            </File>
            <File>
              <FileName>plc_lib.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\source\platform\plc_task\plc_lib.lib</FilePath>
            </File>
            <File>
              <FileName>plc_reftable.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\platform\plc_task\plc_reftable.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>mods</GroupName>
          <Files>
            <File>
              <FileName>LANGUAGE.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\LANGUAGE.c</FilePath>
            </File>
            <File>
              <FileName>PARA_GROUPS.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\PARA_GROUPS.c</FilePath>
            </File>
            <File>
              <FileName>REF_TABLES.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\REF_TABLES.c</FilePath>
            </File>
            <File>
              <FileName>fun.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\fun.c</FilePath>
            </File>
            <File>
              <FileName>appDevice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\appDevice.c</FilePath>
            </File>
            <File>
              <FileName>mod_bi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_bi.c</FilePath>
            </File>
            <File>
              <FileName>mod_bo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_bo.c</FilePath>
            </File>
            <File>
              <FileName>mod_cur.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_cur.c</FilePath>
            </File>
            <File>
              <FileName>mod_getAngle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_getAngle.c</FilePath>
            </File>
            <File>
              <FileName>mod_measData.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_measData.c</FilePath>
            </File>
            <File>
              <FileName>mod_overCur.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_overCur.c</FilePath>
            </File>
            <File>
              <FileName>mod_rmtCtl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_rmtCtl.c</FilePath>
            </File>
            <File>
              <FileName>mod_selfChk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_selfChk.c</FilePath>
            </File>
            <File>
              <FileName>mod_vol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_vol.c</FilePath>
            </File>
            <File>
              <FileName>mod_CTFail.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_CTFail.c</FilePath>
            </File>
            <File>
              <FileName>mod_nonElec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_nonElec.c</FilePath>
            </File>
            <File>
              <FileName>mod_noVol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_noVol.c</FilePath>
            </File>
            <File>
              <FileName>mod_overLoad.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_overLoad.c</FilePath>
            </File>
            <File>
              <FileName>mod_overVol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_overVol.c</FilePath>
            </File>
            <File>
              <FileName>mod_PTFail.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_PTFail.c</FilePath>
            </File>
            <File>
              <FileName>mod_sglOverCur.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_sglOverCur.c</FilePath>
            </File>
            <File>
              <FileName>mod_underFreq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_underFreq.c</FilePath>
            </File>
            <File>
              <FileName>mod_general.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_general.c</FilePath>
            </File>
            <File>
              <FileName>mod_negOverCur.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_negOverCur.c</FilePath>
            </File>
            <File>
              <FileName>mod_overFreq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_overFreq.c</FilePath>
            </File>
            <File>
              <FileName>mod_overSpeed.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_overSpeed.c</FilePath>
            </File>
            <File>
              <FileName>mod_rvsPwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_rvsPwr.c</FilePath>
            </File>
            <File>
              <FileName>mod_underExc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_underExc.c</FilePath>
            </File>
            <File>
              <FileName>mod_synCtr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\mods\mod_synCtr.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>main</GroupName>
          <Files>
            <File>
              <FileName>umain.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\source\main_task\umain.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lib</GroupName>
          <Files>
            <File>
              <FileName>arm_cortexM4lf_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\lib\arm_cortexM4lf_math.lib</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.6.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.7.7" url="http://www.keil.com/pack/" vendor="ARM" version="5.9.0"/>
        <targetInfos>
          <targetInfo name="X13606_CUBEMX_GD32F470ZGT6"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>X13606_CUBEMX_GD32F470ZGT6</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
