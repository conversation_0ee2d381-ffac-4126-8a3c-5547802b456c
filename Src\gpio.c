/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    gpio.c
  * @brief   This file provides code for the configuration
  *          of all used GPIO pins.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "gpio.h"

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/*----------------------------------------------------------------------------*/
/* Configure GPIO                                                             */
/*----------------------------------------------------------------------------*/
/* USER CODE BEGIN 1 */

/* USER CODE END 1 */

/** Configure pins as
        * Analog
        * Input
        * Output
        * EVENT_OUT
        * EXTI
*/
void MX_GPIO_Init(void)
{

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOH_CLK_ENABLE();
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOE_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();

}

/* USER CODE BEGIN 2 */

//NOTEL B码相关函数
//B码口线设为上升沿中断
void B_code_GPIO_RISING(void)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  HAL_NVIC_DisableIRQ(EXTI9_5_IRQn);

  __HAL_RCC_GPIOD_CLK_ENABLE();

  /*Configure GPIO pin : PD9 */
  GPIO_InitStruct.Pin = GPIO_PIN_9;
  GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING;   //PD9 上升沿中断
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

  /* EXTI interrupt init*/
  HAL_NVIC_SetPriority(EXTI9_5_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(EXTI9_5_IRQn);
}

//B码口线设为下降沿中断
void B_code_GPIO_FALLING(void)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  HAL_NVIC_DisableIRQ(EXTI9_5_IRQn);

  __HAL_RCC_GPIOD_CLK_ENABLE();

  /*Configure GPIO pin : PD9 */
  GPIO_InitStruct.Pin = GPIO_PIN_9;
  GPIO_InitStruct.Mode = GPIO_MODE_IT_FALLING;   //PD9 下降沿中断
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

  /* EXTI interrupt init*/
  HAL_NVIC_SetPriority(EXTI9_5_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(EXTI9_5_IRQn);

}

extern uint8_t g_Bcode_state;
extern uint32_t g_Bcode_time;
extern uint8_t g_Bcode_flg ;         //可读取标志
//B码口线初始化
void B_code_Init(void)
{
  HAL_NVIC_DisableIRQ(EXTI9_5_IRQn);

  B_code_GPIO_RISING();                         //上升沿中断
  g_Bcode_state = 1;
  g_Bcode_time = 0;
  g_Bcode_flg = 0;
}

void B_code_deInit(void)
{
  HAL_NVIC_DisableIRQ(EXTI9_5_IRQn);
  HAL_GPIO_DeInit(GPIOD, GPIO_PIN_9);           //取消PD9的外部中断

  g_Bcode_time= 0;
  g_Bcode_flg = 0;
}
//NOTE

/* USER CODE END 2 */
