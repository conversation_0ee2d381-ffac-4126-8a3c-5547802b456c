#ifndef _MOD_OVERVOL_H_
#define _MOD_OVERVOL_H_

#include "task_queue.h"
#include "fun.h"

typedef struct
{
	char* dpName;
	
	//输入信号
	int* uab; //
	int* u_max;
	
	
	//输入信号
	unsigned char qd_ov;
	unsigned char op_ov;
    unsigned char op_ov_alarm;
	//unsigned char ovbi_ov;	//软压板
	//输出模拟量
	
	//定值
	//unsigned char vbi_ov;	//软压板
	unsigned char setSwit_ov;	//控制字
	unsigned char setSwit_ov_alarm;	//控制字
	int set_u_ov;	//过压保护定值
	int set_qd_u;	//0.95倍过压保护定值
	int set_qd_u_alarm;
	unsigned int set_t_ov;	//过压时间定值
    int set_u_ov_alarm;
	unsigned int set_t_ov_alarm;
	int trip_matrix; //跳闸矩阵

	//内部变量
	int timebuf_qd;	//起动元件时间
	int timebuf_op[3];
	int timebuf_op_alarm[3];
	int timebuf_dly;
	int timebuf_dly_alarm;
	int timebuf_zk;
	unsigned char flg_ov_qd_val;
	unsigned char flg_ov_qd_val_alarm;
	unsigned char flg_ov_op_val;
	unsigned char flg_ov_op_val_alarm;

}s_modOverVol;

int initModOverVol(s_modOverVol* dp,char* dpName);
int initModParmOverVol(s_modOverVol* dp);

#endif