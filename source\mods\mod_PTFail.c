#include "mod_PTFail.h"

/*Pt 断线*/

void runModPTFail(s_modPTFail* dp)
{
	unsigned char flg_con;
	
	if (*dp->flg_qdzc == 0)
	{
		flg_con = ((*dp->u1)<30*DEFAULT_DIV_VAL) || ((*dp->u2)>=8*DEFAULT_DIV_VAL);
		dp->pt_dx = delayRelay(flg_con & dp->setSwit_pt_dx, &dp->timebuf_pt, 10000);
	}	
	
	return;
}
int initModPTFail(s_modPTFail* dp,char* dpName)
{
	dp->dpName = dpName;
	//注册开关量
	regBI(dpName,"pt_dx", 0,&dp->pt_dx);
	//注册模拟量
	
	//注册告警信息
	regChkElement(&dp->pt_dx);
	
	//加入队列
	addTaskLevel2(runModPTFail, dp);
	return SUCC;
}

int initModParmPTFail(s_modPTFail* dp)
{
	dp->setSwit_pt_dx = getSetByDesc("setSwit_pt_dx");
	
	return SUCC;
}