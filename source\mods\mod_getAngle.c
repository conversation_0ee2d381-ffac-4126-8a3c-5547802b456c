#include "mod_getAngle.h"

void runModGetAngle(s_modGetAngle* dp)
{
	unsigned char nowPtr;
	nowPtr = circAft(dp->ptr_angle_cal, ANGLE_NUM, 1);	//下一个角度
	angleCalc((dp->angleOutF + nowPtr), dp->vector1_array[nowPtr] , dp->vector2_array[nowPtr], 1);       //每次只算一个
	dp->angleOut[nowPtr] = dp->angleOutF[nowPtr]*1000;
	dp->ptr_angle_cal = nowPtr;
	char temp ;
	
	//计算同期角差   
	if(dp->set_mode_syn == 0)
	    dp->vector1_array_a[0] =  &dp->vector_modVol[0*2];	 //Ua	
	else
	if(dp->set_mode_syn == 1)
	    dp->vector1_array_a[0] =  &dp->vector_modVol[1*2];  //Ub
    else
	if(dp->set_mode_syn == 2)
	    dp->vector1_array_a[0] =  &dp->vector_modVol[2*2];  //Uc
    else
	if(dp->set_mode_syn == 3)
	    dp->vector1_array_a[0] =  &dp->vector_modVol[5*2];  //Uab
    else
	if(dp->set_mode_syn == 4)
	    dp->vector1_array_a[0] =  &dp->vector_modVol[6*2];  //Ubc
    else
	if(dp->set_mode_syn == 5)
	    dp->vector1_array_a[0] =  &dp->vector_modVol[7*2];  //Uca
    else
		dp->vector1_array_a[0] =  &dp->vector_modVol[5*2];  //Uab
     	 
	dp->vector2_array_a[0] =  &dp->vector_modVol[4 *2];		//Ux


	angleCalc((&dp->angle_dif), dp->vector1_array_a[0] , dp->vector2_array_a[0], 1); 
	dp->angle_dif_show = dp->angle_dif*1000;//TEST

	return;
}

int initModGetAngle(s_modGetAngle* dp, char* dpName)
{
	dp->dpName = dpName;
	//注册开关量

	//注册模拟量 这里都设的是3位小数
	regANA(dpName, "ang01", 0, &dp->angleOut[0], 3, "°"); //Ia-Ib
	regANA(dpName, "ang02", 0, &dp->angleOut[1], 3, "°"); //Ib-Ic
	regANA(dpName, "ang03", 0, &dp->angleOut[2], 3, "°"); //Ic-Ia
	regANA(dpName, "ang04", 0, &dp->angleOut[3], 3, "°"); //I0-Ia
	regANA(dpName, "ang05", 0, &dp->angleOut[4], 3, "°"); //U0-Ua
	regANA(dpName, "ang06", 0, &dp->angleOut[5], 3, "°"); //Ux-Ua
	regANA(dpName, "ang07", 0, &dp->angleOut[6], 3, "°"); //Ua-Ub
	regANA(dpName, "ang08", 0, &dp->angleOut[7], 3, "°"); //Ub-Uc
	regANA(dpName, "ang09", 0, &dp->angleOut[8], 3, "°"); //Uc-Ua
	regANA(dpName, "ang10", 0, &dp->angleOut[9], 3, "°"); //I0-3U0

	regANA(dpName, "angle_dif_show", 0, &dp->angle_dif_show, 2, "°"); //同期角差

	//加入队列
	addTaskLevel2(runModGetAngle, dp);
	return SUCC;
}

int initModParmGetAngle(s_modGetAngle* dp)
{
    dp->set_mode_syn = getSetByDesc("orig_set_mode_syn");     	//同期抽取电压
    dp->set_mode_syn /= 10000;
	
	return SUCC;
}
